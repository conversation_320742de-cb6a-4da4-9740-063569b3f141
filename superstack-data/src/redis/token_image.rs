use std::collections::HashMap;

use redis::AsyncCommands;

use crate::{
    error::Result,
    postgres::Image,
    redis::{
        utils::{
            parse_bytes_from_redis_value_map, parse_i64_from_redis_value_map,
            parse_string_from_redis_value_map,
        },
        value::RedisValue,
        RedisClient,
    },
};

const TOKEN_IMAGE_KEY_PREFIX: &str = "image:";

const FIELD_IMAGE_ID: &'static str = "i";
const FIELD_IMAGE_TYPE: &'static str = "ty";
const FIELD_IMAGE_URL: &'static str = "u";
const FIELD_IMAGE_DATA: &'static str = "d";
const FIELD_TIMESTAMP_SECONDS: &'static str = "ts";

impl RedisClient {
    fn construct_token_image_key(image_id: &str) -> String {
        format!("{}{}", TOKEN_IMAGE_KEY_PREFIX, image_id)
    }

    fn token_image_to_hash(image: &Image) -> Vec<(String, RedisValue)> {
        vec![
            (FIELD_IMAGE_ID.to_string(), RedisValue::String(image.image_id.clone())),
            (FIELD_IMAGE_TYPE.to_string(), RedisValue::String(image.image_type.clone())),
            (FIELD_IMAGE_URL.to_string(), RedisValue::String(image.image_url.clone())),
            (FIELD_IMAGE_DATA.to_string(), RedisValue::Bytes(image.image_data.clone())),
            (FIELD_TIMESTAMP_SECONDS.to_string(), RedisValue::Int64(image.timestamp_seconds)),
        ]
    }

    fn hash_to_token_image(hash_data: &HashMap<String, redis::Value>) -> Result<Image> {
        Ok(Image {
            image_id: parse_string_from_redis_value_map(hash_data, FIELD_IMAGE_ID)?,
            image_type: parse_string_from_redis_value_map(hash_data, FIELD_IMAGE_TYPE)?,
            image_url: parse_string_from_redis_value_map(hash_data, FIELD_IMAGE_URL)?,
            image_data: parse_bytes_from_redis_value_map(hash_data, FIELD_IMAGE_DATA)?,
            timestamp_seconds: parse_i64_from_redis_value_map(hash_data, FIELD_TIMESTAMP_SECONDS)?,
        })
    }

    pub async fn set_image(&self, image: &Image) -> Result<()> {
        let key = Self::construct_token_image_key(&image.image_id);
        let mut conn = self.get_connection();
        let hash_data = Self::token_image_to_hash(image);
        let _: () = conn.hset_multiple(key, &hash_data).await?;
        Ok(())
    }

    pub async fn set_empty_image(&self, image_id: &str) -> Result<()> {
        let key = Self::construct_token_image_key(image_id);
        let mut conn = self.get_connection();
        let mut image = Image::default();
        image.image_id = image_id.to_string();
        let hash_data = Self::token_image_to_hash(&image);
        let _: () = conn.hset_multiple(&key, &hash_data).await?;
        Ok(())
    }

    pub async fn get_image(&self, image_id: &str) -> Result<Option<Image>> {
        let key = Self::construct_token_image_key(image_id);
        let mut conn = self.get_connection();
        let hash_data: HashMap<String, redis::Value> = conn.hgetall(&key).await?;
        if hash_data.is_empty() {
            return Ok(None);
        }
        Ok(Some(Self::hash_to_token_image(&hash_data)?))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_set_and_get_image() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();

        let image_id = "test_image_123";
        let test_image = Image {
            image_id: image_id.to_string(),
            image_type: "image/png".to_string(),
            image_url: "https://example.com/test.png".to_string(),
            image_data: vec![0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A], // PNG header
            timestamp_seconds: 1704067200,                                    // 2024-01-01 00:00:00
        };

        // Set the image
        redis_client.set_image(&test_image).await.unwrap();

        // Get the image back
        let retrieved_image = redis_client.get_image(image_id).await.unwrap().unwrap();

        // Verify all fields match
        assert_eq!(test_image.image_id, retrieved_image.image_id);
        assert_eq!(test_image.image_type, retrieved_image.image_type);
        assert_eq!(test_image.image_url, retrieved_image.image_url);
        assert_eq!(test_image.image_data, retrieved_image.image_data);
        assert_eq!(test_image.timestamp_seconds, retrieved_image.timestamp_seconds);
        tracing::info!("{}", serde_json::json!(retrieved_image));

        tracing::info!("✅ set_and_get_image test passed");
    }

    #[tokio::test]
    async fn test_get_non_existent_image() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();

        let result = redis_client.get_image("non_existent_image_id").await.unwrap();
        assert!(result.is_none());

        tracing::info!("✅ get_non_existent_image test passed");
    }

    #[tokio::test]
    async fn test_binary_data_handling() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();

        let image_id = "binary_test_image";

        // Create test image with various binary data patterns
        let mut binary_data = Vec::new();
        // Add various byte patterns including null bytes, high values, etc.
        for i in 0..=255u8 {
            binary_data.push(i);
        }

        let test_image = Image {
            image_id: image_id.to_string(),
            image_type: "application/octet-stream".to_string(),
            image_url: "https://example.com/binary.bin".to_string(),
            image_data: binary_data.clone(),
            timestamp_seconds: 1704067200,
        };

        // Set and get the image
        redis_client.set_image(&test_image).await.unwrap();
        let retrieved_image = redis_client.get_image(image_id).await.unwrap().unwrap();

        // Verify binary data is preserved exactly
        assert_eq!(test_image.image_data, retrieved_image.image_data);
        assert_eq!(binary_data.len(), retrieved_image.image_data.len());

        for (i, (&original, &retrieved)) in
            binary_data.iter().zip(retrieved_image.image_data.iter()).enumerate()
        {
            assert_eq!(
                original, retrieved,
                "Byte mismatch at position {}: expected {}, got {}",
                i, original, retrieved
            );
        }

        tracing::info!("✅ binary_data_handling test passed");
    }

    #[tokio::test]
    async fn test_different_image_types() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();

        let test_cases = vec![
            ("jpeg_image", "image/jpeg", vec![0xFF, 0xD8, 0xFF, 0xE0]), // JPEG header
            ("gif_image", "image/gif", vec![0x47, 0x49, 0x46, 0x38, 0x39, 0x61]), // GIF89a header
            ("webp_image", "image/webp", vec![0x52, 0x49, 0x46, 0x46]), // RIFF header
            ("svg_image", "image/svg+xml", b"<svg>test</svg>".to_vec()),
        ];

        for (image_id, image_type, image_data) in test_cases {
            let test_image = Image {
                image_id: image_id.to_string(),
                image_type: image_type.to_string(),
                image_url: format!("https://example.com/{}.img", image_id),
                image_data: image_data.clone(),
                timestamp_seconds: 1704067200 + image_id.len() as i64, // Unique timestamp
            };

            redis_client.set_image(&test_image).await.unwrap();
            let retrieved_image = redis_client.get_image(image_id).await.unwrap().unwrap();

            assert_eq!(test_image.image_id, retrieved_image.image_id);
            assert_eq!(test_image.image_type, retrieved_image.image_type);
            assert_eq!(test_image.image_url, retrieved_image.image_url);
            assert_eq!(test_image.image_data, retrieved_image.image_data);
            assert_eq!(test_image.timestamp_seconds, retrieved_image.timestamp_seconds);
        }

        tracing::info!("✅ different_image_types test passed");
    }

    #[tokio::test]
    async fn test_empty_image_data() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let redis_client = RedisClient::from_config().await.unwrap();

        let image_id = "empty_image";
        let test_image = Image {
            image_id: image_id.to_string(),
            image_type: "application/octet-stream".to_string(),
            image_url: "https://example.com/empty".to_string(),
            image_data: Vec::new(), // Empty binary data
            timestamp_seconds: 1704067200,
        };

        redis_client.set_image(&test_image).await.unwrap();
        let retrieved_image = redis_client.get_image(image_id).await.unwrap().unwrap();

        assert_eq!(test_image.image_data, retrieved_image.image_data);
        assert!(retrieved_image.image_data.is_empty());

        tracing::info!("✅ empty_image_data test passed");
    }
}
