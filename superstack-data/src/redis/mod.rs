pub mod distributed_lock;
pub mod perp_state;
pub mod perp_users;
pub mod pool_metadata;
pub mod pool_statistic;
pub mod status;
pub mod token_metadata;
pub mod token_statistic;
pub mod utils;
pub mod value;

use redis::{
    aio::MultiplexedConnection,
    cluster::{ClusterClient, ClusterClientBuilder},
    cluster_async::ClusterConnection,
    Client, TlsCertificates,
};
use tokio::sync::OnceCell;

use crate::{error::Result, postgres::PostgresDatabase};

#[derive(Clone)]
pub struct RedisClient {
    _client: Client,
    connection: MultiplexedConnection,
}

impl RedisClient {
    async fn from_config() -> Result<Self> {
        let config = crate::config::Config::get();
        let redis_url = &config.redis_url;
        let client = RedisClient::new(redis_url).await?;
        Ok(client)
    }

    pub async fn get_instance() -> &'static Self {
        static INSTANCE: OnceCell<RedisClient> = OnceCell::const_new();
        INSTANCE
            .get_or_init(|| async {
                RedisClient::from_config().await.expect("Failed to create Redis client")
            })
            .await
    }

    pub async fn new(url: &str) -> Result<Self> {
        // This can be called successfully at most once in any process execution.
        let _ = rustls::crypto::aws_lc_rs::default_provider().install_default();

        // For Redis Cloud, the URL should be in the format:
        // without TLS: redis://username:password@host:port
        // with TLS: rediss://username:password@host:port
        let client = if url.starts_with("rediss://") {
            let tls_certs = TlsCertificates { client_tls: None, root_cert: None };
            Client::build_with_tls(url, tls_certs)?
        } else {
            Client::open(url)?
        };
        let connection = client.get_multiplexed_async_connection().await?;
        Ok(Self { _client: client, connection })
    }

    pub fn get_connection(&self) -> MultiplexedConnection {
        self.connection.clone()
    }

    #[cfg(test)]
    pub async fn create_test_index(&self) -> Result<()> {
        let mut conn = self.get_connection();

        // Check if index exists
        let existing_indexes: Vec<String> = redis::cmd("FT._LIST").query_async(&mut conn).await?;
        if existing_indexes.contains(&"test_idx".to_string()) {
            tracing::info!("Index already exists");
            return Ok(());
        }

        // Using raw command string
        let _: () = redis::cmd("FT.CREATE")
            .arg("test_idx")
            .arg("ON")
            .arg("HASH")
            .arg("PREFIX")
            .arg("1")
            .arg("test:")
            .arg("SCHEMA")
            .arg("name")
            .arg("TEXT")
            .arg("SORTABLE")
            .arg("email")
            .arg("TAG")
            .arg("SORTABLE")
            .query_async(&mut conn)
            .await?;
        tracing::info!("Index created");

        Ok(())
    }

    #[cfg(test)]
    pub async fn drop_test_index(&self) -> Result<()> {
        let mut conn = self.get_connection();
        let _: () = redis::cmd("FT.DROPINDEX").arg("test_idx").query_async(&mut conn).await?;
        tracing::info!("Index deleted");
        Ok(())
    }

    pub async fn init_redis_with_database(&self) -> Result<()> {
        let redis_status = self.get_status().await?;
        tracing::info!("Redis status: {:?}", redis_status);
        if !redis_status.is_initialized {
            tracing::error!("Redis is not initialized, init it with database");
            let db = PostgresDatabase::get_indexer_db().await;
            let batch_size = 10000;
            let mut offset = 0;
            let mut token_count = 0;
            let mut pool_count = 0;
            loop {
                match db.get_token_statistics(batch_size, offset).await {
                    Ok(token_statistics) => {
                        if token_statistics.is_empty() {
                            break;
                        }
                        for token_statistic in token_statistics {
                            match self
                                .contains_token(
                                    token_statistic.chain,
                                    &token_statistic.token_address,
                                )
                                .await
                            {
                                Ok(true) => {
                                    continue;
                                }
                                Ok(false) => {
                                    if self.set_token(&token_statistic).await.is_ok() {
                                        token_count += 1;
                                    }
                                }
                                Err(e) => {
                                    tracing::error!("Failed to check token: {}", e);
                                    tokio::time::sleep(std::time::Duration::from_secs(1)).await;
                                    continue;
                                }
                            }
                        }
                        offset += batch_size;
                        tracing::info!("Processed {} tokens", offset);
                    }
                    Err(e) => {
                        tracing::error!("Failed to get token statistics: {}", e);
                        tokio::time::sleep(std::time::Duration::from_secs(1)).await;
                        continue;
                    }
                }
            }

            let mut offset = 0;
            loop {
                match db.get_pool_statistics(batch_size, offset).await {
                    Ok(pool_statistics) => {
                        if pool_statistics.is_empty() {
                            break;
                        }
                        for pool_statistic in pool_statistics {
                            match self
                                .contains_pool(pool_statistic.chain, &pool_statistic.pool_address)
                                .await
                            {
                                Ok(true) => {
                                    continue;
                                }
                                Ok(false) => {
                                    if self.set_pool(&pool_statistic).await.is_ok() {
                                        pool_count += 1;
                                    }
                                }
                                Err(e) => {
                                    tracing::error!("Failed to check pool: {}", e);
                                    tokio::time::sleep(std::time::Duration::from_secs(1)).await;
                                    continue;
                                }
                            }
                        }
                        offset += batch_size;
                        tracing::info!("Processed {} pools", offset);
                    }
                    Err(e) => {
                        tracing::error!("Failed to get pool statistics: {}", e);
                        tokio::time::sleep(std::time::Duration::from_secs(1)).await;
                        continue;
                    }
                }
            }

            let mut retry_count = 0;
            loop {
                match self.mark_as_initialized(token_count, pool_count).await {
                    Ok(_) => {
                        tracing::info!(
                            "Initialized redis with database, token count: {}, pool count: {}",
                            token_count,
                            pool_count
                        );
                        return Ok(());
                    }
                    Err(e) => {
                        retry_count += 1;
                        if retry_count > 60 {
                            tracing::error!("Failed to mark redis as initialized: {}", e);
                            return Err(anyhow::anyhow!(
                                "Failed to mark redis as initialized: {}",
                                e
                            )
                            .into());
                        }
                        tracing::error!(
                            "Failed to mark redis as initialized, retry: {}, error: {}",
                            retry_count,
                            e
                        );
                        tokio::time::sleep(std::time::Duration::from_secs(1)).await;
                        continue;
                    }
                }
            }
        }

        Ok(())
    }
}

#[derive(Clone)]
pub struct RedisClusterClient {
    _client: ClusterClient,
    connection: ClusterConnection,
}

impl RedisClusterClient {
    pub async fn new(url: &str) -> Result<Self> {
        // This can be called successfully at most once in any process execution.
        let _ = rustls::crypto::aws_lc_rs::default_provider().install_default();

        // For Redis Cloud, the URL should be in the format:
        // redis://username:password@host:port
        let client = ClusterClientBuilder::new(vec![url]).tls(redis::TlsMode::Insecure).build()?;
        let connection = client.get_async_connection().await?;
        Ok(Self { _client: client, connection })
    }

    pub fn get_connection(&self) -> ClusterConnection {
        self.connection.clone()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use redis::AsyncCommands;

    #[tokio::test]
    async fn test_redis_client() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let client = RedisClient::from_config().await.unwrap();
        let mut connection = client.get_connection();

        // Test with PING command which is always allowed
        let pong: String = connection.ping().await.unwrap();
        assert_eq!(pong, "PONG");

        // Test with SET and GET commands
        let _: () = connection.set("test-key", "test-value").await.unwrap();
        let value: String = connection.get("test-key").await.unwrap();
        assert_eq!(value, "test-value");

        let _: () = connection.del("test-key").await.unwrap();

        client.create_test_index().await.unwrap();
        client.drop_test_index().await.unwrap();
    }

    #[tokio::test]
    #[ignore]
    async fn test_redis_cluster_client() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let config = crate::config::Config::get();
        let client = RedisClusterClient::new(&config.redis_url).await.unwrap();
        let mut connection = client.get_connection();

        // Test with PING command which is always allowed
        let pong: String = connection.ping().await.unwrap();
        assert_eq!(pong, "PONG");

        // Test with SET and GET commands
        let _: () = connection.set("test-key", "test-value").await.unwrap();
        let value: String = connection.get("test-key").await.unwrap();
        assert_eq!(value, "test-value");
    }

    #[tokio::test]
    async fn test_redis_client_search_tokens() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        use crate::{data_provider::filter::*, postgres::Chain};

        let client = RedisClient::from_config().await.unwrap();

        let filter = TokensFilter {
            limit: 100,
            offset: 0,
            chain: Some(vec![Chain::Solana]),
            time: Some(Time::Hour24),
            trend: Some(Trend::Trending),
            ..Default::default()
        };

        let start_time = std::time::Instant::now();
        let tokens = client.search_tokens_by_filter(&filter).await.unwrap();
        tracing::info!("search {} tokens time: {:?}", tokens.len(), start_time.elapsed());
    }
}
