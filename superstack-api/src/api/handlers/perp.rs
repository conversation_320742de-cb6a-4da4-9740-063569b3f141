use std::collections::HashMap;

use axum::{
    extract::{Path, Query, State},
    http::{header, HeaderMap, HeaderValue, StatusCode},
    Json,
};
use bigdecimal::{BigDecimal, ToPrimitive};
use chrono::Utc;
use serde::{Deserialize, Serialize};
use superstack_data::postgres::indexer::{perp_books::PerpBooks, PerpInfo, PerpState};

use super::ErrorResponse;
use crate::{models::StorageState, perp_category::ensure_perp_category_manager};

const USDC_DECIMALS: u8 = 6;
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerpDetail {
    pub info: PerpInfo,
    pub state: PerpState,
    pub state_series: HashMap<String, PerpStateSeriesResponse>,
    pub categorys: Vec<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PerpStateSeriesResponse {
    pub mark_px: f64,
    pub market_cap: f64,
    pub funding: f64,
    pub open_interest: f64,
    pub long_ntl: f64,
    pub short_ntl: f64,
    pub long_traders: u64,
    pub short_traders: u64,
}

#[derive(Debug, Deserialize)]
pub struct PerpListQuery {
    // Pagination
    pub limit: Option<i64>,
    pub offset: Option<i64>,

    // Range filters
    pub volume_min: Option<f64>,
    pub volume_max: Option<f64>,
    pub funding_min: Option<f64>,
    pub funding_max: Option<f64>,
    pub open_interest_min: Option<f64>,
    pub open_interest_max: Option<f64>,

    // Category filter
    pub category: Option<String>, // "ai", "layer1", "layer2", "defi", "gaming", "meme"

    // Sort options
    pub sort_by: Option<String>, /* "24h_volume", "funding", "open_interest", "mark_px",
                                  * "market_cap" */
    pub sort_desc: Option<bool>, // true for DESC, false for ASC
}

#[derive(Debug, Serialize)]
pub struct PerpListResponse {
    pub perps: Vec<PerpDetail>,
    pub total: usize,
}

impl PerpListQuery {
    fn validate(&self) -> Result<(), String> {
        // Validate pagination
        if let Some(limit) = self.limit {
            if limit <= 0 || limit > 1000 {
                return Err("Limit must be between 1 and 1000".to_string());
            }
        }

        if let Some(offset) = self.offset {
            if offset < 0 {
                return Err("Offset must be non-negative".to_string());
            }
        }

        // Validate range filters
        if let (Some(min), Some(max)) = (self.volume_min, self.volume_max) {
            if min > max {
                return Err("Volume min cannot be greater than max".to_string());
            }
        }

        if let (Some(min), Some(max)) = (self.funding_min, self.funding_max) {
            if min > max {
                return Err("Funding min cannot be greater than max".to_string());
            }
        }

        if let (Some(min), Some(max)) = (self.open_interest_min, self.open_interest_max) {
            if min > max {
                return Err("Open interest min cannot be greater than max".to_string());
            }
        }

        // Validate sort_by
        if let Some(ref sort_by) = self.sort_by {
            match sort_by.as_str() {
                "24h_volume" | "funding" | "open_interest" | "mark_px" | "market_cap" => {},
                _ => return Err("Invalid sort_by field. Valid values: 24h_volume, funding, open_interest, mark_px, market_cap".to_string()),
            }
        }

        // Validate category
        if let Some(ref category) = self.category {
            let manager = ensure_perp_category_manager();
            if !manager.get_all_categories().contains(category) {
                return Err(
                    "Invalid category. Valid values: ai, layer1, layer2, defi, gaming, meme"
                        .to_string(),
                );
            }
        }

        Ok(())
    }
}

pub async fn get_perp_list(
    State(state): State<StorageState>,
    Query(query): Query<PerpListQuery>,
) -> Result<Json<PerpListResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Validate query parameters
    if let Err(err) = query.validate() {
        return Err((StatusCode::BAD_REQUEST, Json(ErrorResponse::new(&err))));
    }

    // Set defaults
    let limit = query.limit.unwrap_or(300);
    let offset = query.offset.unwrap_or(0);
    let sort_desc = query.sort_desc.unwrap_or(true); // Default to DESC

    // Build filter ranges
    let volume_range = match (query.volume_min, query.volume_max) {
        (Some(min), Some(max)) => Some((min, max)),
        (Some(min), None) => Some((min, f64::MAX)),
        (None, Some(max)) => Some((0.0, max)),
        (None, None) => None,
    };

    let funding_range = match (query.funding_min, query.funding_max) {
        (Some(min), Some(max)) => Some((min, max)),
        (Some(min), None) => Some((min, f64::MAX)),
        (None, Some(max)) => Some((f64::MIN, max)),
        (None, None) => None,
    };

    let open_interest_range = match (query.open_interest_min, query.open_interest_max) {
        (Some(min), Some(max)) => Some((min, max)),
        (Some(min), None) => Some((min, f64::MAX)),
        (None, Some(max)) => Some((0.0, max)),
        (None, None) => None,
    };

    // Get filtered and sorted perp states
    let perp_states = state
        .indexer_db
        .get_filtered_and_sorted_perp_states(
            volume_range,
            funding_range,
            open_interest_range,
            query.sort_by.as_deref(),
            sort_desc,
            limit,
            offset,
        )
        .await
        .map_err(|e| {
            tracing::error!("Failed to get perp states: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to fetch perp data")),
            )
        })?;

    // Get corresponding perp infos and apply category filter
    let mut perp_details = Vec::with_capacity(perp_states.len());
    let category_manager = ensure_perp_category_manager();

    let now = Utc::now().timestamp_millis();
    for mut perp_state in perp_states {
        // Try to get corresponding perp info
        let perp_info = state
            .indexer_db
            .get_perp_info(perp_state.perp_exchange, &perp_state.perp_id)
            .await
            .map_err(|e| {
                tracing::error!("Failed to get perp info for {}: {}", perp_state.perp_id, e);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Failed to fetch perp info")),
                )
            })?;

        let (info, categorys) = if let Some(info) = perp_info {
            // Apply category filter if specified
            if let Some(ref filter_category) = query.category {
                if !category_manager.is_symbol_in_category(&perp_state.perp_id, filter_category) {
                    continue;
                }
            }

            // Get categorys for this symbol
            let categorys = category_manager
                .get_categories_by_symbol(&perp_state.perp_id)
                .map(|cat| cat.iter().map(|c| c.clone()).collect())
                .unwrap_or_else(Vec::new);

            (info, categorys)
        } else {
            // Log missing perp info but continue
            tracing::warn!(
                "Missing perp info for exchange: {:?}, id: {}",
                perp_state.perp_exchange,
                perp_state.perp_id
            );
            continue;
        };

        // Get perp state detail
        let perp_state_detail =
            state.redis_client.get_perp_state(&perp_state.perp_id).await.map_err(|e| {
                tracing::error!(
                    "Failed to get perp state detail for {}: {}",
                    perp_state.perp_id,
                    e
                );
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::new("Failed to fetch perp state detail")),
                )
            })?;

        if let Some(detail) = perp_state_detail {
            // populate perp state fields
            perp_state.long_ntl =
                BigDecimal::from_biguint(detail.long_ntl.into(), USDC_DECIMALS as _).to_f64();
            perp_state.short_ntl =
                BigDecimal::from_biguint(detail.short_ntl.into(), USDC_DECIMALS as _).to_f64();
            perp_state.long_traders = Some(detail.long_traders);
            perp_state.short_traders = Some(detail.short_traders);
            perp_state.long_entry = Some(detail.long_entry);
            perp_state.short_entry = Some(detail.short_entry);
        } else {
            tracing::warn!(
                "Missing perp state detail for exchange: {:?}, id: {}",
                perp_state.perp_exchange,
                perp_state.perp_id
            );
        }

        // Get perp state series: 1h 6h 24h 7d
        const TIME_FRAMES: [(&str, i64); 4] = [
            ("1h", 1 * 60 * 60 * 1000),
            ("6h", 6 * 60 * 60 * 1000),
            ("24h", 24 * 60 * 60 * 1000),
            ("7d", 7 * 24 * 60 * 60 * 1000),
        ];

        let mut perp_state_series = HashMap::new();
        for (time_frame, time_frame_millis) in TIME_FRAMES {
            let opt = state
                .indexer_db
                .get_perp_state_series_earlier_than(
                    perp_state.perp_exchange,
                    &perp_state.perp_id,
                    now - time_frame_millis,
                )
                .await
                .map_err(|_| {
                    (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse::new("Failed to fetch perp state series")),
                    )
                })?;

            let series = match opt {
                Some(series) => series,
                None => {
                    tracing::warn!(
                        "Missing perp state series for exchange: {:?}, id: {}",
                        perp_state.perp_exchange,
                        perp_state.perp_id
                    );
                    continue;
                }
            };

            perp_state_series.insert(
                time_frame.to_owned(),
                PerpStateSeriesResponse {
                    mark_px: series.mark_px,
                    market_cap: series.market_cap,
                    funding: series.funding,
                    open_interest: series.open_interest,
                    long_ntl: series.long_ntl,
                    short_ntl: series.short_ntl,
                    long_traders: series.long_traders,
                    short_traders: series.short_traders,
                },
            );
        }

        perp_details.push(PerpDetail {
            info,
            state: perp_state,
            state_series: perp_state_series,
            categorys,
        });
    }

    Ok(Json(PerpListResponse { total: perp_details.len(), perps: perp_details }))
}

/// Response for perp categories endpoint
#[derive(Debug, Serialize)]
pub struct PerpCategoriesResponse {
    pub categories: Vec<String>,
}

/// Response for perp category details endpoint
#[derive(Debug, Serialize)]
pub struct PerpCategoryDetailResponse {
    pub category: String,
    pub symbols: Vec<String>,
}

/// Get all available perp categories
pub async fn get_perp_categories(
) -> Result<Json<PerpCategoriesResponse>, (StatusCode, Json<ErrorResponse>)> {
    let category_manager = ensure_perp_category_manager();
    let categories = category_manager.get_all_categories();

    Ok(Json(PerpCategoriesResponse { categories }))
}

#[derive(Debug, Deserialize)]
pub struct CategoryDetailQuery {
    pub category: String,
}

/// Get symbols for a specific category
pub async fn get_perp_category_detail(
    Query(params): Query<CategoryDetailQuery>,
) -> Result<Json<PerpCategoryDetailResponse>, (StatusCode, Json<ErrorResponse>)> {
    let category_manager = ensure_perp_category_manager();

    if let Some(symbols) = category_manager.get_symbols_by_category(&params.category) {
        Ok(Json(PerpCategoryDetailResponse { category: params.category, symbols: symbols.clone() }))
    } else {
        Err((StatusCode::NOT_FOUND, Json(ErrorResponse::new("Category not found"))))
    }
}

/// Get perp books for a specific perp
pub async fn get_perp_books(
    State(state): State<StorageState>,
    Path(perp_id): Path<String>,
) -> Result<(HeaderMap, Json<PerpBooks>), (StatusCode, Json<ErrorResponse>)> {
    let (headers, perp_books) = state
        .redis_client
        .get_perp_books(&perp_id)
        .await
        .map_err(|e| (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::new(e.to_string()))))?
        .ok_or_else(|| {
            (StatusCode::NOT_FOUND, Json(ErrorResponse::new("perp books not found".to_string())))
        })
        .map(|books| {
            let mut headers = axum::http::HeaderMap::new();
            // perp book valid for 15 minutes
            headers.insert(header::CACHE_CONTROL, HeaderValue::from_static("public, max-age=900"));
            (headers, books)
        })?;

    Ok((headers, Json(perp_books)))
}
