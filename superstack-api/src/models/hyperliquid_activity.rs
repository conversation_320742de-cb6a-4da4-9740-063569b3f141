use anyhow::Result;
use chrono::Utc;
use serde::{Deserialize, Serialize};

use super::{activity::DbWalletActivity, ActivityType};
use crate::constant::CHAIN_HYPERCORE;

/// Get correct token decimals for HyperEVM tokens
fn get_hyperevm_token_decimals(token_symbol: &str) -> i16 {
    match token_symbol.to_uppercase().as_str() {
        "USDC" => 6,
        "USDT" => 6,
        "SOL" => 9, // SOL has 9 decimals even on HyperEVM
        "BTC" | "BITCOIN" => 18,
        "ETH" | "ETHEREUM" => 18,
        _ => 18, // Default for EVM tokens
    }
}

/// Check if an address is a Hyperliquid system address
/// System addresses are used for HyperCore <-> HyperEVM transfers
/// See https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/hyperevm/hypercore-less-than-greater-than-hyperevm-transfers
fn is_system_address(address: &str) -> bool {
    // Convert to lowercase for consistent comparison
    let addr = address.to_lowercase();

    // HYPE system address: ******************************************
    if addr == "******************************************" {
        return true;
    }

    // Token system addresses: 0x20000000000000000000000000000000000000XX
    // where XX is the token index in big-endian format
    if addr.starts_with("0x2000000000000000000000000000000000000") && addr.len() == 42 {
        // Check if the last 3 characters are valid hex (token index)
        let suffix = &addr[39..42]; // Get last 3 chars after "0x2000000000000000000000000000000000000"
        return suffix.chars().all(|c| c.is_ascii_hexdigit());
    }

    false
}

/// Hyperliquid Fill data structure based on Hyperliquid TypeScript definitions
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct HyperliquidFill {
    /// Asset symbol
    pub coin: String,
    /// Price (string format)
    pub px: String,
    /// Size (string format)
    pub sz: String,
    /// Order side ("B" = Bid/Buy, "A" = Ask/Sell)
    pub side: String,
    /// Timestamp when the trade occurred (in ms since epoch)
    pub time: i64,
    /// Start position size (string format)
    #[serde(rename = "startPosition")]
    pub start_position: String,
    /// Direction indicator for frontend display
    pub dir: String,
    /// Realized PnL (string format)
    #[serde(rename = "closedPnl")]
    pub closed_pnl: String,
    /// L1 transaction hash
    pub hash: String,
    /// Order ID
    pub oid: i64,
    /// Indicates if the fill was a taker order
    pub crossed: bool,
    /// Fee charged or rebate received (string format)
    pub fee: String,
    /// Unique transaction identifier for a partial fill of an order
    pub tid: i64,
    /// Client Order ID (optional)
    pub cloid: Option<String>,
    /// Liquidation details (optional)
    pub liquidation: Option<HyperliquidFillLiquidation>,
    /// Token in which the fee is denominated (e.g., "USDC")
    #[serde(rename = "feeToken")]
    pub fee_token: String,
}

/// Liquidation details for a trade fill
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct HyperliquidFillLiquidation {
    /// Address of the liquidated user
    #[serde(rename = "liquidatedUser")]
    pub liquidated_user: String,
    /// Mark price at the time of liquidation (string format)
    #[serde(rename = "markPx")]
    pub mark_px: String,
    /// Liquidation method
    pub method: String, // "market" | "backstop"
}

/// Hyperliquid NonFundingLedgerUpdate data structure
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct HyperliquidNonFundingLedgerUpdate {
    /// Timestamp of the update (in ms since epoch)
    pub time: i64,
    /// L1 transaction hash
    pub hash: String,
    /// Update details
    pub delta: HyperliquidLedgerDelta,
}

/// Different types of ledger updates
#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum HyperliquidLedgerDelta {
    #[serde(rename = "internalTransfer")]
    InternalTransfer {
        /// Amount transferred in USDC (string format)
        usdc: String,
        /// Initiator's address
        user: String,
        /// Destination address
        destination: String,
        /// Transfer fee (string format)
        fee: String,
    },
    #[serde(rename = "accountClassTransfer")]
    AccountClassTransfer {
        /// Amount transferred in USDC (string format)
        usdc: String,
        /// Indicates if the transfer is to the perpetual account
        #[serde(rename = "toPerp")]
        to_perp: bool,
    },
    #[serde(rename = "deposit")]
    Deposit {
        /// Amount deposited in USDC (string format)
        usdc: String,
    },
    #[serde(rename = "withdraw")]
    Withdraw {
        /// Amount withdrawn in USDC (string format)
        usdc: String,
        /// Unique nonce for the withdrawal request
        nonce: i64,
        /// Withdrawal fee (string format)
        fee: String,
    },
    #[serde(rename = "spotTransfer")]
    SpotTransfer {
        /// Token symbol
        token: String,
        /// Amount transferred (string format)
        amount: String,
        /// Equivalent USDC value (string format)
        #[serde(rename = "usdcValue")]
        usdc_value: String,
        /// Initiator's address
        user: String,
        /// Destination address
        destination: String,
        /// Transfer fee (string format)
        fee: String,
        /// Fee in native token (string format)
        #[serde(rename = "nativeTokenFee")]
        native_token_fee: String,
    },
    #[serde(rename = "liquidation")]
    Liquidation {
        /// Total notional value of liquidated positions (string format)
        #[serde(rename = "liquidatedNtlPos")]
        liquidated_ntl_pos: String,
        /// Account value at liquidation time (string format)
        #[serde(rename = "accountValue")]
        account_value: String,
        /// Leverage type for liquidated positions
        #[serde(rename = "leverageType")]
        leverage_type: String, // "Cross" | "Isolated"
        /// Details of each liquidated position
        #[serde(rename = "liquidatedPositions")]
        liquidated_positions: Vec<HyperliquidLiquidatedPosition>,
    },
    #[serde(rename = "rewardsClaim")]
    RewardsClaim {
        /// Amount of rewards claimed (string format)
        amount: String,
    },
    #[serde(rename = "subAccountTransfer")]
    SubAccountTransfer {
        /// Amount transferred in USDC (string format)
        usdc: String,
        /// Source sub-account ID
        #[serde(rename = "subAccount")]
        sub_account: i32,
        /// Destination sub-account ID
        #[serde(rename = "destSubAccount")]
        dest_sub_account: i32,
    },
    #[serde(rename = "vaultCreate")]
    VaultCreate {
        /// Vault address
        vault: String,
        /// Amount in USDC (string format)
        usdc: String,
        /// Creation fee (string format)
        fee: String,
    },
    #[serde(rename = "vaultDeposit")]
    VaultDeposit {
        /// Vault address
        vault: String,
        /// Amount deposited in USDC (string format)
        usdc: String,
    },
    #[serde(rename = "vaultDistribution")]
    VaultDistribution {
        /// Vault address
        vault: String,
        /// Amount distributed in USDC (string format)
        usdc: String,
    },
    #[serde(rename = "vaultWithdraw")]
    VaultWithdraw {
        /// Vault address
        vault: String,
        /// Amount withdrawn in USDC (string format)
        usdc: String,
    },
}

/// Details of a liquidated position
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct HyperliquidLiquidatedPosition {
    /// Asset symbol of the liquidated position
    pub coin: String,
    /// Signed position size liquidated (string format)
    pub szi: String,
}

/// Order parameters from frontend
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct OrderParameters {
    /// Array of orders
    pub orders: Vec<OrderDetail>,
    /// Order grouping strategy
    pub grouping: String, // "na" | "normalTpsl" | "positionTpsl"
}

/// Individual order detail
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct OrderDetail {
    /// Asset ID
    pub a: i32,
    /// Position side (true for long, false for short)
    pub b: bool,
    /// Price
    pub p: String,
    /// Size (in base currency units)
    pub s: String,
    /// Is reduce-only?
    pub r: bool,
    /// Order type
    pub t: OrderType,
    /// Client Order ID
    pub c: Option<String>,
}

/// Order type definition
#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(untagged)]
pub enum OrderType {
    Limit { limit: LimitOrderParams },
    Trigger { trigger: TriggerOrderParams },
}

/// Limit order parameters
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct LimitOrderParams {
    /// Time-in-force
    pub tif: String, // TIF enum
}

/// Trigger order parameters
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct TriggerOrderParams {
    /// Is market order?
    #[serde(rename = "isMarket")]
    pub is_market: bool,
    /// Trigger price
    #[serde(rename = "triggerPx")]
    pub trigger_px: String,
    /// Indicates whether it is take profit or stop loss
    pub tpsl: String, // "tp" | "sl"
}

/// TWAP order parameters
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct TwapOrderParameters {
    /// Asset ID
    pub a: i32,
    /// Position side (true for long, false for short)
    pub b: bool,
    /// Size (in base currency units)
    pub s: String,
    /// Is reduce-only?
    pub r: bool,
    /// TWAP duration in minutes
    pub m: i32,
    /// Enable random order timing
    pub t: bool,
}

/// Order response from API
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct OrderResponse {
    /// Response details
    pub response: OrderResponseData,
}

/// Order response data
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct OrderResponseData {
    /// Response type
    #[serde(rename = "type")]
    pub response_type: String,
    /// Response data
    pub data: OrderResponseDataInner,
}

/// Order response data inner
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct OrderResponseDataInner {
    /// Array of statuses
    pub statuses: Vec<OrderStatus>,
}

/// Order status in response
#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(untagged)]
pub enum OrderStatus {
    Resting { resting: RestingOrderStatus },
    Filled { filled: FilledOrderStatus },
    Error { error: String },
}

/// Resting order status
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct RestingOrderStatus {
    /// Order ID
    pub oid: i64,
    /// Client Order ID
    pub cloid: Option<String>,
}

/// Filled order status
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct FilledOrderStatus {
    /// Total size filled
    #[serde(rename = "totalSz")]
    pub total_sz: String,
    /// Average price of fill
    #[serde(rename = "avgPx")]
    pub avg_px: String,
    /// Order ID
    pub oid: i64,
    /// Client Order ID
    pub cloid: Option<String>,
}

/// TWAP order response
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct TwapOrderResponse {
    /// Response details
    pub response: TwapOrderResponseData,
}

/// TWAP order response data
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct TwapOrderResponseData {
    /// Response type
    #[serde(rename = "type")]
    pub response_type: String,
    /// Response data
    pub data: TwapOrderResponseDataInner,
}

/// TWAP order response data inner
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct TwapOrderResponseDataInner {
    /// Status of the operation
    pub status: TwapOrderStatusResponse,
}

/// TWAP order status response
#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(untagged)]
pub enum TwapOrderStatusResponse {
    Running { running: RunningTwapStatus },
    Error { error: String },
}

/// Running TWAP status
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct RunningTwapStatus {
    /// TWAP ID
    #[serde(rename = "twapId")]
    pub twap_id: i64,
}

/// Perpetual order from frontend (single object, not array)
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct PerpsOrder {
    /// Order parameters (optional)
    pub order: Option<OrderParameters>,
    /// TWAP order parameters (optional)
    #[serde(rename = "twapOrder")]
    pub twap_order: Option<TwapOrderParameters>,
    /// Order response (optional)
    #[serde(rename = "orderResponse")]
    pub order_response: Option<Vec<OrderStatus>>,
    /// TWAP order response (optional)
    #[serde(rename = "twapOrderResponse")]
    pub twap_order_response: Option<TwapOrderStatusResponse>,
    /// Trading pair symbol
    pub symbol: String,
    /// Token image URL
    #[serde(rename = "tokenImage")]
    pub token_image: String,
    /// Leverage multiplier (optional, will be calculated if missing)
    #[serde(default)]
    pub leverage: Option<f64>,
    /// Leverage type: "Cross" | "Isolated" (optional, defaults to "Cross")
    #[serde(rename = "leverageType", default = "default_leverage_type")]
    pub leverage_type: String, // "Cross" | "Isolated"
}

/// Default leverage type for PerpsOrder
fn default_leverage_type() -> String {
    "Cross".to_string()
}

/// Order update status
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct OrderUpdate {
    /// Order details
    pub order: Order,
    /// Order processing status
    pub status: String, // "open" | "filled" | "canceled" | etc.
    /// Timestamp when the status was last updated (in ms since epoch)
    #[serde(rename = "statusTimestamp")]
    pub status_timestamp: i64,
}

/// Order details
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct Order {
    /// Asset symbol
    pub coin: String,
    /// Order side ("B" = Bid/Buy, "A" = Ask/Sell)
    pub side: String, // "B" | "A"
    /// Limit price
    #[serde(rename = "limitPx")]
    pub limit_px: String,
    /// Size
    pub sz: String,
    /// Order ID
    pub oid: i64,
    /// Timestamp when the order was placed (in ms since epoch)
    pub timestamp: i64,
    /// Original size at order placement
    #[serde(rename = "origSz")]
    pub orig_sz: String,
    /// Client Order ID
    pub cloid: Option<String>,
    /// Indicates if the order is reduce-only
    #[serde(rename = "reduceOnly")]
    pub reduce_only: Option<bool>,
}

/// TWAP slice fills history
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct WsUserTwapSliceFills {
    /// User's address
    pub user: String,
    /// Array of TWAP slice fills
    #[serde(rename = "twapSliceFills")]
    pub twap_slice_fills: Vec<TwapSliceFill>,
    /// Whether this is an initial snapshot
    #[serde(rename = "isSnapshot")]
    pub is_snapshot: Option<bool>,
}

/// TWAP slice fill
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct TwapSliceFill {
    /// Fill details for the TWAP slice
    pub fill: HyperliquidFill, // Reuse existing Fill structure but without cloid and liquidation
    /// ID of the TWAP
    #[serde(rename = "twapId")]
    pub twap_id: i64,
}

/// Deposit activity from frontend
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct Deposit {
    /// Timestamp of the deposit (in ms since epoch)
    pub time: i64,
    /// Transaction hash
    pub hash: String,
    /// Deposit amount (string format)
    pub amount: String,
    /// Token symbol
    pub token: String,
    /// Token image URL
    #[serde(rename = "tokenImage")]
    pub token_image: Option<String>,
    /// Destination address
    #[serde(rename = "toAddress")]
    pub to_address: String,
    /// Source address (optional)
    #[serde(rename = "fromAddress")]
    pub from_address: Option<String>,
    /// Deposit fee (string format)
    pub fee: Option<String>,
    /// Fee token symbol
    #[serde(rename = "feeToken")]
    pub fee_token: Option<String>,
    /// Deposit type: "direct" | "bridge" | "exchange"
    #[serde(rename = "depositType")]
    pub deposit_type: Option<String>,
    /// Source platform
    pub source: Option<String>,
    /// Network name
    pub network: Option<String>,
    /// Number of confirmations
    pub confirmations: Option<i32>,
    /// Block number
    #[serde(rename = "blockNumber")]
    pub block_number: Option<i64>,
    /// Gas used
    #[serde(rename = "gasUsed")]
    pub gas_used: Option<String>,
    /// Gas price
    #[serde(rename = "gasPrice")]
    pub gas_price: Option<String>,
    /// Additional metadata
    pub metadata: Option<serde_json::Value>,
}

/// Withdraw activity from frontend
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct Withdraw {
    /// Timestamp of the withdrawal (in ms since epoch)
    pub time: i64,
    /// Transaction hash
    pub hash: String,
    /// Withdrawal amount (string format)
    pub amount: String,
    /// Token symbol
    pub token: String,
    /// Token image URL
    #[serde(rename = "tokenImage")]
    pub token_image: Option<String>,
    /// Source address
    #[serde(rename = "fromAddress")]
    pub from_address: String,
    /// Destination address
    #[serde(rename = "toAddress")]
    pub to_address: String,
    /// Withdrawal fee (string format)
    pub fee: String,
    /// Fee token symbol
    #[serde(rename = "feeToken")]
    pub fee_token: String,
    /// Withdrawal nonce
    pub nonce: i64,
    /// Withdrawal type: "external" | "internal" | "bridge"
    #[serde(rename = "withdrawType")]
    pub withdraw_type: Option<String>,
    /// Destination platform
    pub destination: Option<String>,
    /// Network name
    pub network: Option<String>,
    /// Estimated arrival timestamp
    #[serde(rename = "estimatedArrival")]
    pub estimated_arrival: Option<i64>,
    /// Withdrawal status: "pending" | "confirmed" | "failed"
    pub status: Option<String>,
    /// Number of confirmations
    pub confirmations: Option<i32>,
    /// Block number
    #[serde(rename = "blockNumber")]
    pub block_number: Option<i64>,
    /// Gas used
    #[serde(rename = "gasUsed")]
    pub gas_used: Option<String>,
    /// Gas price
    #[serde(rename = "gasPrice")]
    pub gas_price: Option<String>,
    /// Additional metadata
    pub metadata: Option<serde_json::Value>,
}

/// Nested activities structure for frontend format
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct NestedActivities {
    /// Fill activities from frontend
    #[serde(rename = "userFills")]
    pub user_fills: Option<Vec<HyperliquidFill>>,
    /// Non-funding ledger updates from frontend
    #[serde(rename = "userNonFundingLedgerUpdates")]
    pub user_non_funding_ledger_updates: Option<Vec<HyperliquidNonFundingLedgerUpdate>>,
    /// Perpetual order from frontend (single object)
    #[serde(rename = "perpsOrder")]
    pub perps_order: Option<PerpsOrder>,
    /// Order updates from frontend
    #[serde(rename = "orderUpdates")]
    pub order_updates: Option<Vec<OrderUpdate>>,
    /// TWAP fills history from frontend
    #[serde(rename = "twapFillsHistory")]
    pub twap_fills_history: Option<WsUserTwapSliceFills>,
    /// Deposit activities from frontend
    pub deposits: Option<Vec<Deposit>>,
    /// Withdraw activities from frontend
    pub withdraws: Option<Vec<Withdraw>>,
}

/// Request structure for the activity recording endpoint
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct RecordActivityRequest {
    /// Wallet address
    pub wallet_address: String,
    /// Whether this is Hyperliquid mainnet
    #[serde(rename = "isHyperliquidMainnet")]
    pub is_hyperliquid_mainnet: bool,
    /// Activities data from frontend
    pub activities: NestedActivities,
}

/// Aggregated order data for wallet activity
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct AggregatedOrderData {
    /// Order ID
    pub oid: i64,
    /// Asset symbol
    pub coin: String,
    /// Order side ("B" = Buy, "A" = Sell)
    pub side: String,
    /// Total aggregated size
    pub total_size: f64,
    /// Volume-weighted average price
    pub avg_price: f64,
    /// Total USD value
    pub total_usd_value: f64,
    /// Total fees paid
    pub total_fees: f64,
    /// Fee token
    pub fee_token: String,
    /// Total closed P&L
    pub total_closed_pnl: f64,
    /// Number of fills
    pub fill_count: usize,
    /// Order completion timestamp (last fill time)
    pub completion_time: i64,
    /// Order type (Market/Limit)
    pub order_type: String,
    /// Transaction hash from the most recent fill
    pub hash: String,
    /// Fill summary for metadata (simplified)
    pub fill_summary: Vec<FillSummary>,
}

/// Simplified fill summary for metadata storage
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct FillSummary {
    pub tid: i64,
    pub px: String,
    pub sz: String,
    pub time: i64,
    pub fee: String,
}

/// Response structure for the activity recording endpoint
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct RecordActivityResponse {
    /// Success status
    pub success: bool,
    /// Status message
    pub message: String,
    /// Number of activities processed
    pub processed_count: usize,
    /// Number of activities that failed to process
    pub failed_count: usize,
    /// Error details for failed activities (optional)
    pub errors: Option<Vec<String>>,
}

/// Helper function to parse string amounts with custom decimals
pub fn parse_amount_with_decimals(amount_str: &str, decimals: u8) -> Result<i64> {
    let amount: f64 = amount_str.parse()?;
    let multiplier = 10_f64.powi(decimals as i32);
    Ok((amount * multiplier) as i64)
}

/// Helper function to parse USDC amounts (6 decimals)
pub fn parse_usdc_amount(amount_str: &str) -> Result<i64> {
    parse_amount_with_decimals(amount_str, 6)
}

/// Helper function to convert parsed USDC amount to USD value
pub fn usdc_to_usd_value(amount_str: &str) -> Result<Option<f64>> {
    let amount = parse_usdc_amount(amount_str)?;
    Ok(if amount != 0 { Some((amount as f64) / 1_000_000.0) } else { None })
}

// Removed create_metadata macro - using direct serde_json::json! for cleaner, minimal metadata

impl HyperliquidFill {
    /// Determine if this is a reduce-only operation based on direction and side
    fn is_reduce_only(&self) -> bool {
        // Check if direction indicates closing a position
        self.dir.to_lowercase().contains("close") || self.dir.to_lowercase().contains("reduce")
    }

    /// Determine position action for frontend display
    fn get_position_action(&self) -> String {
        let is_buy = self.side == "B";
        let is_reduce = self.is_reduce_only();

        match (is_buy, is_reduce) {
            (true, false) => "Open Long".to_string(),
            (true, true) => "Close Short".to_string(),
            (false, false) => "Open Short".to_string(),
            (false, true) => "Close Long".to_string(),
        }
    }
}

impl HyperliquidFill {
    /// Convert HyperEvm Fill to unified DbWalletActivity format
    pub fn to_db_activity(&self, wallet_address: &str) -> Result<DbWalletActivity> {
        use chrono::Utc;

        // All userfill are perpetual trades on Hyperliquid
        let activity_type = ActivityType::PerpTrade;

        // Parse amounts with correct decimals
        let token_decimals = get_hyperevm_token_decimals(&self.coin);
        let token_amount = parse_amount_with_decimals(&self.sz, token_decimals as u8)?;

        // Calculate trade notional value for USD value
        let price = self
            .px
            .parse::<f64>()
            .map_err(|_| anyhow::anyhow!("Invalid price format: {}", self.px))?;
        let size = self
            .sz
            .parse::<f64>()
            .map_err(|_| anyhow::anyhow!("Invalid size format: {}", self.sz))?;
        let notional_value = price * size;

        // USD value should be the trade notional value, not PnL
        let usd_value = Some(notional_value);

        // Determine if this is a reduce-only operation
        let is_reduce_only = self.is_reduce_only();
        let position_action = self.get_position_action();

        // Create minimal metadata with only display-essential fields
        let mut metadata = serde_json::json!({
            "side": &self.side,
            "price": &self.px,
            "trading_pair": format!("{}/{}", self.coin, self.fee_token),
            "order_type": if self.crossed { "Market" } else { "Limit" },
            "direction": &self.dir, // Add direction information from dir field
            "fee": &self.fee,       // Add fee to metadata for display
            "fee_token": &self.fee_token,
            "reduce_only": is_reduce_only,
            "position_action": position_action
            // Note: Leverage information should be provided by the data source (PerpsOrder)
            // HyperliquidFill doesn't contain leverage information, so we don't add it here
        });

        // Add PnL only if meaningful (non-zero) - this is separate from trade value
        if !self.closed_pnl.is_empty() && self.closed_pnl != "0" && self.closed_pnl != "0.0" {
            metadata["pnl"] = serde_json::Value::String(self.closed_pnl.clone());
        }

        // Add liquidation info only if present (important for user awareness)
        if let Some(liquidation) = &self.liquidation {
            metadata["liquidation"] = serde_json::json!({
                "liquidated_user": liquidation.liquidated_user,
                "mark_price": liquidation.mark_px
            });
        }

        // Handle system operations with zero hash
        let tx_signature =
            if crate::hyperliquid_parser::HyperliquidParser::is_system_operation_hash(&self.hash) {
                // Store original hash in metadata for reference
                metadata["original_hash"] = serde_json::Value::String(self.hash.clone());
                metadata["is_system_operation"] = serde_json::Value::Bool(true);

                // Determine operation type from metadata
                let operation_type =
                    crate::hyperliquid_parser::HyperliquidParser::determine_operation_type(
                        &metadata,
                    );

                // Generate unique signature for system operations
                crate::hyperliquid_parser::HyperliquidParser::generate_unique_tx_signature(
                    wallet_address,
                    &self.hash,
                    self.time,
                    &operation_type,
                    Some(self.tid as u64), // Use transaction ID as sequence
                )
            } else {
                self.hash.clone()
            };

        Ok(DbWalletActivity {
            wallet_address: wallet_address.to_string(),
            tx_signature,
            activity_type,
            token_mint: Some(self.coin.clone()),
            token_decimals: Some(token_decimals),
            token_amount: Some(token_amount),
            base_mint: None, // Perpetual contracts don't have base token exchange
            base_decimals: None,
            base_amount: None, // No actual base token exchange in perp trades
            usd_value,
            timestamp: self.time / 1000, // Convert milliseconds to seconds
            block_time: Some(self.time / 1000), // Convert milliseconds to seconds
            slot: None,                  // HyperEvm doesn't have slots
            chain: CHAIN_HYPERCORE,
            metadata: Some(metadata),
            created_at: Utc::now().timestamp(),
        })
    }
}

impl PerpsOrder {
    /// Convert PerpsOrder to unified DbWalletActivity format
    /// Extracts fill information from orderResponse to calculate actual trading values
    pub fn to_db_activity(
        &self,
        wallet_address: &str,
        tx_hash: &str,
        timestamp: i64,
        is_hyperliquid_mainnet: bool,
    ) -> Result<DbWalletActivity> {
        use chrono::Utc;

        // Extract asset symbol from the trading pair
        let asset = self.symbol.split('/').next().unwrap_or(&self.symbol).to_string();
        let token_decimals = get_hyperevm_token_decimals(&asset);

        // Initialize values for filled orders
        let mut token_amount: Option<i64> = None;
        let mut usd_value: Option<f64> = None;

        // Extract fill information from order response
        let mut avg_price: Option<f64> = None;
        if let Some(order_response) = &self.order_response {
            for response in order_response {
                // Check if this is a filled order status
                if let OrderStatus::Filled { filled } = response {
                    // Parse filled size and average price
                    if let (Ok(total_sz), Ok(avg_px)) =
                        (filled.total_sz.parse::<f64>(), filled.avg_px.parse::<f64>())
                    {
                        // Calculate token amount in smallest unit with correct decimals
                        let decimals_multiplier = 10_f64.powi(token_decimals as i32);
                        token_amount = Some((total_sz * decimals_multiplier) as i64);

                        // Calculate USD value from filled amount and average price
                        usd_value = Some(total_sz * avg_px);

                        // Store average price for metadata
                        avg_price = Some(avg_px);

                        tracing::debug!(
                            "Extracted fill info: size={}, avg_price={}, usd_value={}",
                            total_sz,
                            avg_px,
                            total_sz * avg_px
                        );
                        break; // Use first filled response
                    }
                }
            }
        }

        // Determine leverage - use provided leverage or calculate from order parameters
        let leverage_value = if let Some(leverage) = self.leverage {
            leverage
        } else {
            // Try to calculate leverage from order parameters
            if let Some(order) = &self.order {
                if let Some(first_order) = order.orders.first() {
                    // For reduce-only orders, try to estimate leverage
                    if first_order.r {
                        // Reduce-only order, estimate based on position side
                        if first_order.b {
                            10.0
                        } else {
                            10.0
                        } // Default to 10x for reduce-only
                    } else {
                        1.0 // Default to 1x for new positions without leverage info
                    }
                } else {
                    1.0
                }
            } else {
                1.0
            }
        };

        // Extract side information from order parameters
        let side = if let Some(order) = &self.order {
            if let Some(first_order) = order.orders.first() {
                // Convert boolean to standard side format: true = Buy (B), false = Sell (A)
                if first_order.b {
                    "B".to_string() // Buy/Long
                } else {
                    "A".to_string() // Sell/Short
                }
            } else {
                "Unknown".to_string()
            }
        } else {
            "Unknown".to_string()
        };

        // Create metadata with order details
        let mut metadata = serde_json::json!({
            "side": side,
            "trading_pair": &self.symbol,
            "leverage": format!("{}x", leverage_value),
            "leverage_type": &self.leverage_type,
            "is_hyperliquid_mainnet": is_hyperliquid_mainnet,
            "token_image": &self.token_image
        });

        // Add avg_price if available (for data completeness scoring)
        if let Some(avg_px) = avg_price {
            metadata["avg_price"] = serde_json::Value::String(avg_px.to_string());
        }

        // Add order parameters if present
        if let Some(order) = &self.order {
            metadata["order_parameters"] = serde_json::to_value(order)?;
        }

        // Add TWAP order parameters if present
        if let Some(twap_order) = &self.twap_order {
            metadata["twap_order_parameters"] = serde_json::to_value(twap_order)?;
        }

        // Add order response if present and extract oid for matching
        if let Some(order_response) = &self.order_response {
            metadata["order_response"] = serde_json::to_value(order_response)?;

            // Extract oid from the first filled order for record matching
            if let Some(first_response) = order_response.first() {
                if let OrderStatus::Filled { filled } = first_response {
                    metadata["oid"] =
                        serde_json::Value::Number(serde_json::Number::from(filled.oid));
                }
            }
        }

        // Add TWAP order response if present and extract twap_id for matching
        if let Some(twap_order_response) = &self.twap_order_response {
            metadata["twap_order_response"] = serde_json::to_value(twap_order_response)?;

            // Extract twap_id for record matching
            if let TwapOrderStatusResponse::Running { running } = twap_order_response {
                metadata["twap_id"] =
                    serde_json::Value::Number(serde_json::Number::from(running.twap_id));
            }
        }

        Ok(DbWalletActivity {
            wallet_address: wallet_address.to_string(),
            tx_signature: tx_hash.to_string(),
            activity_type: ActivityType::PerpTrade,
            token_mint: Some(asset),
            token_decimals: Some(token_decimals),
            token_amount,
            base_mint: None,
            base_decimals: None,
            base_amount: None,
            usd_value,
            timestamp: timestamp / 1000, // Convert milliseconds to seconds
            block_time: Some(timestamp / 1000),
            slot: None, // HyperEvm doesn't have slots
            chain: CHAIN_HYPERCORE,
            metadata: Some(metadata),
            created_at: Utc::now().timestamp(),
        })
    }
}

impl OrderUpdate {
    /// Convert OrderUpdate to unified DbWalletActivity format
    pub fn to_db_activity(&self, wallet_address: &str, tx_hash: &str) -> Result<DbWalletActivity> {
        use chrono::Utc;

        // Get correct decimals for the token
        let token_decimals = get_hyperevm_token_decimals(&self.order.coin);

        // Parse order size for token amount (convert to smallest unit)
        let token_amount = self
            .order
            .sz
            .parse::<f64>()
            .map(|amount| {
                let decimals_multiplier = 10_f64.powi(token_decimals as i32);
                let result = amount * decimals_multiplier;
                if result > i64::MAX as f64 {
                    tracing::warn!(
                        "Token amount overflow for {}: {} * 10^{} = {}, clamping to i64::MAX",
                        self.order.coin,
                        amount,
                        token_decimals,
                        result
                    );
                    i64::MAX
                } else {
                    result as i64
                }
            })
            .unwrap_or(0);

        // Calculate USD value from limit price and size
        let usd_value = self
            .order
            .limit_px
            .parse::<f64>()
            .and_then(|price| self.order.sz.parse::<f64>().map(|size| price * size))
            .ok();

        // Determine position action for frontend display
        let is_buy = self.order.side == "B";
        let is_reduce_only = self.order.reduce_only.unwrap_or(false);
        let position_action = match (is_buy, is_reduce_only) {
            (true, false) => "Open Long",
            (true, true) => "Close Short",
            (false, false) => "Open Short",
            (false, true) => "Close Long",
        };

        // Create metadata with order update details
        let metadata = serde_json::json!({
            "order_id": self.order.oid,
            "status": &self.status,
            "status_timestamp": self.status_timestamp,
            "coin": &self.order.coin,
            "side": &self.order.side,
            "limit_price": &self.order.limit_px,
            "size": &self.order.sz,
            "original_size": &self.order.orig_sz,
            "client_order_id": &self.order.cloid,
            "reduce_only": is_reduce_only,
            "position_action": position_action,
            "order_timestamp": self.order.timestamp
        });

        Ok(DbWalletActivity {
            wallet_address: wallet_address.to_string(),
            tx_signature: tx_hash.to_string(),
            activity_type: ActivityType::PerpTrade,
            token_mint: Some(self.order.coin.clone()),
            token_decimals: Some(token_decimals),
            token_amount: Some(token_amount),
            base_mint: None,
            base_decimals: None,
            base_amount: None,
            usd_value,
            timestamp: self.status_timestamp / 1000, // Convert milliseconds to seconds
            block_time: Some(self.status_timestamp / 1000),
            slot: None, // HyperEvm doesn't have slots
            chain: CHAIN_HYPERCORE,
            metadata: Some(metadata),
            created_at: Utc::now().timestamp(),
        })
    }
}

impl WsUserTwapSliceFills {
    /// Convert WsUserTwapSliceFills to multiple DbWalletActivity records
    pub fn to_db_activities(&self, wallet_address: &str) -> Result<Vec<DbWalletActivity>> {
        let mut activities = Vec::new();

        for twap_fill in &self.twap_slice_fills {
            let activity = twap_fill.to_db_activity(wallet_address)?;
            activities.push(activity);
        }

        Ok(activities)
    }
}

impl TwapSliceFill {
    /// Convert TwapSliceFill to unified DbWalletActivity format
    pub fn to_db_activity(&self, wallet_address: &str) -> Result<DbWalletActivity> {
        // Convert the fill to DbWalletActivity first
        let mut activity = self.fill.to_db_activity(wallet_address)?;

        // Add TWAP-specific metadata
        if let Some(ref mut metadata) = activity.metadata {
            if let Some(metadata_obj) = metadata.as_object_mut() {
                metadata_obj
                    .insert("twap_id".to_string(), serde_json::Value::Number(self.twap_id.into()));
                metadata_obj.insert("is_twap_slice".to_string(), serde_json::Value::Bool(true));
            }
        }

        Ok(activity)
    }
}

impl Deposit {
    /// Convert Deposit to unified DbWalletActivity format
    pub fn to_db_activity(&self, wallet_address: &str) -> Result<DbWalletActivity> {
        // Parse amount for token amount (assume USDC with 6 decimals for now)
        let token_amount = self
            .amount
            .parse::<f64>()
            .map(|amount| (amount * 1e6) as i64) // Assume 6 decimals for USDC
            .unwrap_or(0);

        // USD value is the same as amount for USDC
        let usd_value = self.amount.parse::<f64>().ok();

        // Create metadata with deposit details
        let mut metadata = serde_json::json!({
            "to_address": &self.to_address,
            "token": &self.token
        });

        // Add optional fields if present
        if let Some(from_address) = &self.from_address {
            metadata["from_address"] = serde_json::Value::String(from_address.clone());
        }
        if let Some(fee) = &self.fee {
            metadata["fee"] = serde_json::Value::String(fee.clone());
        }
        if let Some(fee_token) = &self.fee_token {
            metadata["fee_token"] = serde_json::Value::String(fee_token.clone());
        }
        if let Some(deposit_type) = &self.deposit_type {
            metadata["deposit_type"] = serde_json::Value::String(deposit_type.clone());
        }
        if let Some(source) = &self.source {
            metadata["source"] = serde_json::Value::String(source.clone());
        }
        if let Some(network) = &self.network {
            metadata["network"] = serde_json::Value::String(network.clone());
        }
        if let Some(confirmations) = self.confirmations {
            metadata["confirmations"] = serde_json::Value::Number(confirmations.into());
        }
        if let Some(block_number) = self.block_number {
            metadata["block_number"] = serde_json::Value::Number(block_number.into());
        }
        if let Some(gas_used) = &self.gas_used {
            metadata["gas_used"] = serde_json::Value::String(gas_used.clone());
        }
        if let Some(gas_price) = &self.gas_price {
            metadata["gas_price"] = serde_json::Value::String(gas_price.clone());
        }
        if let Some(extra_metadata) = &self.metadata {
            metadata["extra"] = extra_metadata.clone();
        }

        Ok(DbWalletActivity {
            wallet_address: wallet_address.to_string(),
            tx_signature: self.hash.clone(),
            activity_type: ActivityType::Deposit,
            token_mint: Some(self.token.clone()),
            token_decimals: Some(6), // TODO: Get actual decimals from token registry
            token_amount: Some(token_amount),
            base_mint: None,
            base_decimals: None,
            base_amount: None,
            usd_value,
            timestamp: self.time / 1000, // Convert milliseconds to seconds
            block_time: Some(self.time / 1000),
            slot: None, // HyperEvm doesn't have slots
            chain: CHAIN_HYPERCORE,
            metadata: Some(metadata),
            created_at: Utc::now().timestamp(),
        })
    }
}

impl Withdraw {
    /// Convert Withdraw to unified DbWalletActivity format
    pub fn to_db_activity(&self, wallet_address: &str) -> Result<DbWalletActivity> {
        // Parse amount for token amount (assume USDC with 6 decimals for now)
        let token_amount = self
            .amount
            .parse::<f64>()
            .map(|amount| (amount * 1e6) as i64) // Assume 6 decimals for USDC
            .unwrap_or(0);

        // USD value is the same as amount for USDC
        let usd_value = self.amount.parse::<f64>().ok();

        // Create metadata with withdrawal details
        let mut metadata = serde_json::json!({
            "from_address": &self.from_address,
            "to_address": &self.to_address,
            "token": &self.token,
            "fee": &self.fee,
            "fee_token": &self.fee_token,
            "nonce": self.nonce
        });

        // Add optional fields if present
        if let Some(withdraw_type) = &self.withdraw_type {
            metadata["withdraw_type"] = serde_json::Value::String(withdraw_type.clone());
        }
        if let Some(destination) = &self.destination {
            metadata["destination"] = serde_json::Value::String(destination.clone());
        }
        if let Some(network) = &self.network {
            metadata["network"] = serde_json::Value::String(network.clone());
        }
        if let Some(estimated_arrival) = self.estimated_arrival {
            metadata["estimated_arrival"] = serde_json::Value::Number(estimated_arrival.into());
        }
        if let Some(status) = &self.status {
            metadata["status"] = serde_json::Value::String(status.clone());
        }
        if let Some(confirmations) = self.confirmations {
            metadata["confirmations"] = serde_json::Value::Number(confirmations.into());
        }
        if let Some(block_number) = self.block_number {
            metadata["block_number"] = serde_json::Value::Number(block_number.into());
        }
        if let Some(gas_used) = &self.gas_used {
            metadata["gas_used"] = serde_json::Value::String(gas_used.clone());
        }
        if let Some(gas_price) = &self.gas_price {
            metadata["gas_price"] = serde_json::Value::String(gas_price.clone());
        }
        if let Some(extra_metadata) = &self.metadata {
            metadata["extra"] = extra_metadata.clone();
        }

        Ok(DbWalletActivity {
            wallet_address: wallet_address.to_string(),
            tx_signature: self.hash.clone(),
            activity_type: ActivityType::Withdraw,
            token_mint: Some(self.token.clone()),
            token_decimals: Some(6), // TODO: Get actual decimals from token registry
            token_amount: Some(token_amount),
            base_mint: None,
            base_decimals: None,
            base_amount: None,
            usd_value,
            timestamp: self.time / 1000, // Convert milliseconds to seconds
            block_time: Some(self.time / 1000),
            slot: None, // HyperEvm doesn't have slots
            chain: CHAIN_HYPERCORE,
            metadata: Some(metadata),
            created_at: Utc::now().timestamp(),
        })
    }
}

impl HyperliquidNonFundingLedgerUpdate {
    /// Convert HyperEvm NonFundingLedgerUpdate to unified DbWalletActivity format
    pub fn to_db_activity(&self, wallet_address: &str) -> Result<DbWalletActivity> {
        use chrono::Utc;

        let (
            activity_type,
            token_mint,
            token_decimals,
            token_amount,
            base_mint,
            base_decimals,
            base_amount,
            usd_value,
            metadata,
        ) = match &self.delta {
            HyperliquidLedgerDelta::InternalTransfer { usdc, user, destination, fee } => {
                let amount = parse_usdc_amount(&usdc)?;

                // Determine activity type using hybrid approach:
                // 1. First check for system address patterns (official Hyperliquid architecture)
                // 2. If no system addresses, classify based on user's wallet perspective
                let activity_type = if is_system_address(&user) && !is_system_address(&destination)
                {
                    // From system address to user address: HyperCore -> HyperEVM deposit
                    ActivityType::Deposit
                } else if !is_system_address(&user) && is_system_address(&destination) {
                    // From user address to system address: HyperEVM -> HyperCore withdraw
                    ActivityType::Withdraw
                } else if destination == wallet_address {
                    // User receives funds: classify as deposit from user perspective
                    ActivityType::Deposit
                } else if user == wallet_address {
                    // User sends funds: classify as withdraw/send from user perspective
                    ActivityType::Withdraw
                } else {
                    // Neither user nor destination matches wallet: regular transfer
                    ActivityType::Transfer
                };

                let transfer_type = if is_system_address(&user) || is_system_address(&destination) {
                    // System address involved: official cross-chain transfer
                    match activity_type {
                        ActivityType::Deposit => "hypercore_to_hyperevm",
                        ActivityType::Withdraw => "hyperevm_to_hypercore",
                        _ => "system_transfer",
                    }
                } else {
                    // User addresses only: internal transfer from user perspective
                    match activity_type {
                        ActivityType::Deposit => "receive",
                        ActivityType::Withdraw => "send",
                        _ => "internal_transfer",
                    }
                };

                let metadata = serde_json::json!({
                    "from_address": user,
                    "to_address": destination,
                    "fee": fee,
                    "fee_token": "USDC",
                    "transfer_type": transfer_type
                });
                let usd_value = usdc_to_usd_value(&usdc)?;

                (
                    activity_type,
                    Some("USDC".to_string()),
                    Some(6),
                    Some(amount),
                    None, // No base for transfers - fee is in metadata
                    None,
                    None,
                    usd_value,
                    metadata,
                )
            }

            HyperliquidLedgerDelta::AccountClassTransfer { usdc, to_perp } => {
                let amount = parse_usdc_amount(&usdc)?;

                let metadata = serde_json::json!({
                    "transfer_type": if *to_perp { "spot_to_perp" } else { "perp_to_spot" }
                });
                let usd_value = usdc_to_usd_value(&usdc)?;

                (
                    ActivityType::Transfer,
                    Some("USDC".to_string()),
                    Some(6),
                    Some(amount),
                    None,
                    None,
                    None,
                    usd_value,
                    metadata,
                )
            }

            HyperliquidLedgerDelta::Deposit { usdc: _ } => {
                // DISABLED: Use dedicated deposits array instead to avoid conflicts
                return Err(anyhow::anyhow!("Deposit processing disabled in userNonFundingLedgerUpdate - use deposits array"));
            }

            HyperliquidLedgerDelta::Withdraw { usdc: _, nonce: _, fee: _ } => {
                // DISABLED: Use dedicated withdraws array instead to avoid conflicts
                return Err(anyhow::anyhow!("Withdraw processing disabled in userNonFundingLedgerUpdate - use withdraws array"));
            }

            HyperliquidLedgerDelta::SpotTransfer {
                token,
                amount,
                usdc_value,
                user,
                destination,
                fee,
                native_token_fee,
            } => {
                let token_decimals = get_hyperevm_token_decimals(token);
                let token_amount = parse_amount_with_decimals(&amount, token_decimals as u8)?;

                // All spot transfers are categorized as Transfer
                let activity_type = ActivityType::Transfer;

                let metadata = serde_json::json!({
                    "from_address": user,
                    "to_address": destination,
                    "fee": fee,
                    "fee_token": "USDC",
                    "native_token_fee": native_token_fee
                });
                let usd_value = usdc_to_usd_value(&usdc_value)?;

                (
                    activity_type,
                    Some(token.clone()),
                    Some(token_decimals),
                    Some(token_amount),
                    None, // No base for transfers - fee is in metadata
                    None,
                    None,
                    usd_value,
                    metadata,
                )
            }

            HyperliquidLedgerDelta::Liquidation {
                liquidated_ntl_pos,
                account_value,
                leverage_type,
                liquidated_positions,
            } => {
                let _ntl_pos = parse_usdc_amount(&liquidated_ntl_pos)?;
                let acc_val = parse_usdc_amount(&account_value)?;

                let metadata = serde_json::json!({
                    "leverage_type": leverage_type,
                    "account_value": account_value
                });
                let usd_value = usdc_to_usd_value(&liquidated_ntl_pos)?;

                (
                    ActivityType::Liquidation,
                    None,
                    None,
                    None,
                    Some("USDC".to_string()),
                    Some(6),
                    Some(acc_val),
                    usd_value,
                    metadata,
                )
            }

            HyperliquidLedgerDelta::RewardsClaim { amount } => {
                let reward_amount = parse_usdc_amount(&amount)?;
                let metadata = serde_json::json!({});
                let usd_value = usdc_to_usd_value(&amount)?;

                (
                    ActivityType::Deposit,
                    Some("USDC".to_string()),
                    Some(6),
                    Some(reward_amount),
                    None,
                    None,
                    None,
                    usd_value,
                    metadata,
                )
            }

            HyperliquidLedgerDelta::SubAccountTransfer { usdc, sub_account, dest_sub_account } => {
                let amount = parse_usdc_amount(&usdc)?;
                let metadata = serde_json::json!({
                    "transfer_type": "sub_account_transfer",
                    "from_sub_account": sub_account,
                    "to_sub_account": dest_sub_account
                });
                let usd_value = usdc_to_usd_value(&usdc)?;

                (
                    ActivityType::Transfer,
                    Some("USDC".to_string()),
                    Some(6),
                    Some(amount),
                    None,
                    None,
                    None,
                    usd_value,
                    metadata,
                )
            }

            HyperliquidLedgerDelta::VaultCreate { vault, usdc, fee } => {
                let amount = parse_usdc_amount(&usdc)?;
                let metadata = serde_json::json!({
                    "vault_operation": "create",
                    "vault_address": vault,
                    "fee": fee,
                    "fee_token": "USDC"
                });
                let usd_value = usdc_to_usd_value(&usdc)?;

                (
                    ActivityType::Other,
                    Some("USDC".to_string()),
                    Some(6),
                    Some(amount),
                    None, // No base for vault operations - fee is in metadata
                    None,
                    None,
                    usd_value,
                    metadata,
                )
            }

            HyperliquidLedgerDelta::VaultDeposit { vault, usdc } => {
                let amount = parse_usdc_amount(&usdc)?;
                let metadata = serde_json::json!({
                    "vault_operation": "deposit",
                    "vault_address": vault
                });
                let usd_value = usdc_to_usd_value(&usdc)?;

                (
                    ActivityType::Deposit,
                    Some("USDC".to_string()),
                    Some(6),
                    Some(amount),
                    None,
                    None,
                    None,
                    usd_value,
                    metadata,
                )
            }

            HyperliquidLedgerDelta::VaultDistribution { vault, usdc } => {
                let amount = parse_usdc_amount(&usdc)?;
                let metadata = serde_json::json!({
                    "vault_operation": "distribution",
                    "vault_address": vault
                });
                let usd_value = usdc_to_usd_value(&usdc)?;

                (
                    ActivityType::Deposit,
                    Some("USDC".to_string()),
                    Some(6),
                    Some(amount),
                    None,
                    None,
                    None,
                    usd_value,
                    metadata,
                )
            }

            HyperliquidLedgerDelta::VaultWithdraw { vault, usdc } => {
                let amount = parse_usdc_amount(&usdc)?;
                let metadata = serde_json::json!({
                    "vault_operation": "withdraw",
                    "vault_address": vault
                });
                let usd_value = usdc_to_usd_value(&usdc)?;

                (
                    ActivityType::Withdraw,
                    Some("USDC".to_string()),
                    Some(6),
                    Some(amount),
                    None,
                    None,
                    None,
                    usd_value,
                    metadata,
                )
            }
        };

        // Handle system operations with zero hash
        let (tx_signature, final_metadata) =
            if crate::hyperliquid_parser::HyperliquidParser::is_system_operation_hash(&self.hash) {
                // Store original hash in metadata for reference
                let mut enhanced_metadata = metadata;
                enhanced_metadata["original_hash"] = serde_json::Value::String(self.hash.clone());
                enhanced_metadata["is_system_operation"] = serde_json::Value::Bool(true);

                // Determine operation type from metadata
                let operation_type =
                    crate::hyperliquid_parser::HyperliquidParser::determine_operation_type(
                        &enhanced_metadata,
                    );

                // Generate unique signature for system operations
                let unique_signature =
                    crate::hyperliquid_parser::HyperliquidParser::generate_unique_tx_signature(
                        wallet_address,
                        &self.hash,
                        self.time,
                        &operation_type,
                        None, // No sequence ID available for ledger updates
                    );

                (unique_signature, enhanced_metadata)
            } else {
                (self.hash.clone(), metadata)
            };

        Ok(DbWalletActivity {
            wallet_address: wallet_address.to_string(),
            tx_signature,
            activity_type,
            token_mint,
            token_decimals,
            token_amount,
            base_mint,
            base_decimals,
            base_amount,
            usd_value,
            timestamp: self.time / 1000, // Convert milliseconds to seconds
            block_time: Some(self.time / 1000), // Convert milliseconds to seconds
            slot: None,                  // HyperEvm doesn't have slots
            chain: CHAIN_HYPERCORE,
            metadata: Some(final_metadata),
            created_at: Utc::now().timestamp(),
        })
    }
}

impl AggregatedOrderData {
    /// Create aggregated order data from multiple fills with the same oid
    pub fn from_fills(fills: Vec<HyperliquidFill>) -> Result<Self> {
        if fills.is_empty() {
            return Err(anyhow::anyhow!("Cannot create aggregated order from empty fills"));
        }

        // All fills should have the same oid and coin
        let first_fill = &fills[0];
        let oid = first_fill.oid;
        let coin = first_fill.coin.clone();
        let side = first_fill.side.clone();
        let fee_token = first_fill.fee_token.clone();

        // Validate all fills have the same oid
        for fill in &fills {
            if fill.oid != oid {
                return Err(anyhow::anyhow!("All fills must have the same oid"));
            }
            if fill.coin != coin {
                return Err(anyhow::anyhow!("All fills must have the same coin"));
            }
        }

        // Calculate aggregated values
        let mut total_size = 0.0;
        let mut total_value = 0.0;
        let mut total_fees = 0.0;
        let mut total_closed_pnl = 0.0;
        let mut max_time = i64::MIN;
        let mut latest_hash = String::new();
        let mut fill_summary = Vec::new();

        for fill in &fills {
            let size = fill
                .sz
                .parse::<f64>()
                .map_err(|_| anyhow::anyhow!("Invalid size format: {}", fill.sz))?;
            let price = fill
                .px
                .parse::<f64>()
                .map_err(|_| anyhow::anyhow!("Invalid price format: {}", fill.px))?;
            let fee = fill
                .fee
                .parse::<f64>()
                .map_err(|_| anyhow::anyhow!("Invalid fee format: {}", fill.fee))?;
            let closed_pnl = fill.closed_pnl.parse::<f64>().unwrap_or(0.0);

            total_size += size;
            total_value += size * price;
            total_fees += fee;
            total_closed_pnl += closed_pnl;

            // Track the hash from the most recent fill
            if fill.time >= max_time {
                max_time = fill.time;
                latest_hash = fill.hash.clone();
            }

            // Create simplified fill summary
            fill_summary.push(FillSummary {
                tid: fill.tid,
                px: fill.px.clone(),
                sz: fill.sz.clone(),
                time: fill.time,
                fee: fill.fee.clone(),
            });
        }

        // Calculate volume-weighted average price
        let avg_price = if total_size > 0.0 { total_value / total_size } else { 0.0 };

        // Determine order type (Market if any fill is crossed, otherwise Limit)
        let order_type = if fills.iter().any(|f| f.crossed) {
            "Market".to_string()
        } else {
            "Limit".to_string()
        };

        Ok(AggregatedOrderData {
            oid,
            coin,
            side,
            total_size,
            avg_price,
            total_usd_value: total_value,
            total_fees,
            fee_token,
            total_closed_pnl,
            fill_count: fills.len(),
            completion_time: max_time,
            order_type,
            hash: latest_hash,
            fill_summary,
        })
    }

    /// Convert aggregated order data to DbWalletActivity
    /// Note: tx_signature will be ignored during update, existing record's tx_signature is
    /// preserved
    pub fn to_db_activity(&self, wallet_address: &str) -> Result<DbWalletActivity> {
        use chrono::Utc;

        // Use the actual transaction hash as tx_signature
        let tx_signature = self.hash.clone();

        // Convert total size to smallest unit with correct decimals
        let token_decimals = get_hyperevm_token_decimals(&self.coin);
        let decimals_multiplier = 10_f64.powi(token_decimals as i32);
        let token_amount = (self.total_size * decimals_multiplier) as i64;

        // Create simplified metadata - only essential information
        let metadata = serde_json::json!({
            "oid": self.oid,
            "side": self.side,
            "avg_price": self.avg_price,
            "total_size": self.total_size,
            "total_fees": self.total_fees,
            "fee_token": self.fee_token,
            "closedPnl": self.total_closed_pnl,
            "fee": self.total_fees,
            "hash": self.hash,
            "fill_count": self.fill_count,
            "order_type": self.order_type,
            "trading_pair": format!("{}/{}", self.coin, self.fee_token),
            "completion_time": self.completion_time,
            "source": "userfill_aggregated",
            "fills": self.fill_summary
        });

        Ok(DbWalletActivity {
            wallet_address: wallet_address.to_string(),
            tx_signature,
            activity_type: ActivityType::PerpTrade,
            token_mint: Some(self.coin.clone()),
            token_decimals: Some(token_decimals),
            token_amount: Some(token_amount),
            base_mint: None,
            base_decimals: None,
            base_amount: None,
            usd_value: Some(self.total_usd_value),
            timestamp: self.completion_time / 1000, // Use completion time
            block_time: Some(self.completion_time / 1000),
            slot: None, // HyperEvm doesn't have slots
            chain: CHAIN_HYPERCORE,
            metadata: Some(metadata),
            created_at: Utc::now().timestamp(),
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_aggregated_order_data_from_fills() {
        // Create test fills with same oid
        let fills = vec![
            HyperliquidFill {
                coin: "BTC".to_string(),
                px: "50000.0".to_string(),
                sz: "0.1".to_string(),
                side: "B".to_string(),
                time: 1640995200000,
                start_position: "0.0".to_string(),
                dir: "Open Long".to_string(),
                closed_pnl: "0.0".to_string(),
                hash: "0x0000000000000000000000000000000000000000000000000000000000000000"
                    .to_string(),
                oid: 12345,
                crossed: true,
                fee: "2.5".to_string(),
                tid: 1001,
                fee_token: "USDC".to_string(),
                liquidation: None,
                cloid: None,
            },
            HyperliquidFill {
                coin: "BTC".to_string(),
                px: "50100.0".to_string(),
                sz: "0.05".to_string(),
                side: "B".to_string(),
                time: 1640995260000,
                start_position: "0.1".to_string(),
                dir: "Open Long".to_string(),
                closed_pnl: "0.0".to_string(),
                hash: "0x0000000000000000000000000000000000000000000000000000000000000000"
                    .to_string(),
                oid: 12345,
                crossed: true,
                fee: "1.25".to_string(),
                tid: 1002,
                fee_token: "USDC".to_string(),
                liquidation: None,
                cloid: None,
            },
        ];

        let aggregated = AggregatedOrderData::from_fills(fills).unwrap();

        // Verify aggregated data
        assert_eq!(aggregated.oid, 12345);
        assert_eq!(aggregated.coin, "BTC");
        assert_eq!(aggregated.side, "B");
        assert_eq!(aggregated.total_size, 0.15); // 0.1 + 0.05
        assert_eq!(aggregated.total_fees, 3.75); // 2.5 + 1.25
        assert_eq!(aggregated.fill_count, 2);
        assert_eq!(aggregated.order_type, "Market"); // Both fills are crossed

        // Verify volume-weighted average price
        // (0.1 * 50000 + 0.05 * 50100) / 0.15 = (5000 + 2505) / 0.15 = 50033.33...
        let expected_avg_price = (0.1 * 50000.0 + 0.05 * 50100.0) / 0.15;
        assert!((aggregated.avg_price - expected_avg_price).abs() < 0.01);

        // Verify total USD value
        let expected_total_value = 0.1 * 50000.0 + 0.05 * 50100.0;
        assert!((aggregated.total_usd_value - expected_total_value).abs() < 0.01);

        // Verify completion timestamp
        assert_eq!(aggregated.completion_time, 1640995260000);
    }

    #[test]
    fn test_hyperliquid_fill_no_leverage_calculation() {
        // Test that HyperliquidFill doesn't add incorrect leverage calculation
        let fill = HyperliquidFill {
            coin: "ETH".to_string(),
            px: "3929.5".to_string(),
            sz: "0.0057".to_string(),
            side: "A".to_string(),
            time: 1753683184645,
            start_position: "0.0137".to_string(), // This would cause incorrect leverage calculation
            dir: "Close Long".to_string(),
            closed_pnl: "0.32832".to_string(),
            hash: "0x67760154b45107756597042862bfd402079700254b35100df78c57fe63b21049".to_string(),
            oid: 122124045726,
            crossed: true,
            fee: "0.010079".to_string(),
            tid: 791541019643322,
            fee_token: "USDC".to_string(),
            liquidation: None,
            cloid: None,
        };

        let activity = fill.to_db_activity("******************************************").unwrap();

        // Verify that no leverage field is added to metadata
        if let Some(metadata) = &activity.metadata {
            assert!(metadata.get("leverage").is_none(), "HyperliquidFill should not add leverage to metadata");
        }

        // Verify other fields are correct
        assert_eq!(activity.activity_type, ActivityType::PerpTrade);
        assert_eq!(activity.token_mint, Some("ETH".to_string()));

        // Verify position_action is correctly determined
        if let Some(metadata) = &activity.metadata {
            assert_eq!(metadata.get("position_action").and_then(|v| v.as_str()), Some("Close Long"));
        }
    }

    #[test]
    fn test_aggregated_order_data_to_db_activity() {
        let fills = vec![HyperliquidFill {
            coin: "ETH".to_string(),
            px: "3000.0".to_string(),
            sz: "1.0".to_string(),
            side: "A".to_string(),
            time: 1640995200000,
            start_position: "2.0".to_string(),
            dir: "Close Long".to_string(),
            closed_pnl: "100.0".to_string(),
            hash: "0x0000000000000000000000000000000000000000000000000000000000000000".to_string(),
            oid: 67890,
            crossed: false,
            fee: "1.5".to_string(),
            tid: 2001,
            fee_token: "USDC".to_string(),
            liquidation: None,
            cloid: None,
        }];

        let aggregated = AggregatedOrderData::from_fills(fills).unwrap();
        let wallet_address = "******************************************";
        let activity = aggregated.to_db_activity(wallet_address).unwrap();

        // Verify DbWalletActivity fields
        assert_eq!(activity.wallet_address, wallet_address);
        assert_eq!(activity.tx_signature, "userfill_aggregated_67890");
        assert_eq!(activity.activity_type, ActivityType::PerpTrade);
        assert_eq!(activity.token_mint, Some("ETH".to_string()));
        assert_eq!(activity.token_amount, Some(1000000000000000000)); // 1.0 * 1e18
        assert_eq!(activity.usd_value, Some(3000.0));
        assert_eq!(activity.timestamp, 1640995200); // Converted from milliseconds
        assert_eq!(activity.chain, CHAIN_HYPERCORE);

        // Verify metadata contains aggregation info
        let metadata = activity.metadata.unwrap();
        assert_eq!(metadata["oid"], 67890);
        assert_eq!(metadata["side"], "A");
        assert_eq!(metadata["avg_price"], 3000.0);
        assert_eq!(metadata["total_size"], 1.0);
        assert_eq!(metadata["fill_count"], 1);
        assert_eq!(metadata["order_type"], "Limit"); // Not crossed
        assert_eq!(metadata["trading_pair"], "ETH/USDC");
        assert_eq!(metadata["source"], "userfill_aggregated");
    }

    #[test]
    fn test_aggregated_order_data_different_oids_error() {
        let fills = vec![
            HyperliquidFill {
                coin: "BTC".to_string(),
                px: "50000.0".to_string(),
                sz: "0.1".to_string(),
                side: "B".to_string(),
                time: 1640995200000,
                start_position: "0.0".to_string(),
                dir: "Open Long".to_string(),
                closed_pnl: "0.0".to_string(),
                hash: "0x0000000000000000000000000000000000000000000000000000000000000000"
                    .to_string(),
                oid: 12345,
                crossed: true,
                fee: "2.5".to_string(),
                tid: 1001,
                fee_token: "USDC".to_string(),
                liquidation: None,
                cloid: None,
            },
            HyperliquidFill {
                coin: "BTC".to_string(),
                px: "50100.0".to_string(),
                sz: "0.05".to_string(),
                side: "B".to_string(),
                time: 1640995260000,
                start_position: "0.1".to_string(),
                dir: "Open Long".to_string(),
                closed_pnl: "0.0".to_string(),
                hash: "0x0000000000000000000000000000000000000000000000000000000000000000"
                    .to_string(),
                oid: 54321, // Different oid
                crossed: true,
                fee: "1.25".to_string(),
                tid: 1002,
                fee_token: "USDC".to_string(),
                liquidation: None,
                cloid: None,
            },
        ];

        let result = AggregatedOrderData::from_fills(fills);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("All fills must have the same oid"));
    }

    #[test]
    fn test_parse_sample_data() {
        // Test orderUpdates sample
        let order_updates_json = r#"{
            "wallet_address": "******************************************",
            "activities": {
                "orderUpdates": [
                    {
                        "order": {
                            "coin": "SOL",
                            "side": "A",
                            "limitPx": "170.0",
                            "sz": "0.12",
                            "oid": 36105084161,
                            "timestamp": 1753172023118,
                            "origSz": "0.12"
                        },
                        "status": "open",
                        "statusTimestamp": 1753172023118
                    }
                ]
            },
            "isHyperliquidMainnet": false
        }"#;

        let request: RecordActivityRequest = serde_json::from_str(order_updates_json).unwrap();
        assert_eq!(request.wallet_address, "******************************************");
        assert!(!request.is_hyperliquid_mainnet);
        assert!(request.activities.order_updates.is_some());
        assert_eq!(request.activities.order_updates.as_ref().unwrap().len(), 1);

        // Test perpsOrder with limit order sample
        let perps_order_limit_json = r#"{
            "wallet_address": "******************************************",
            "activities": {
                "perpsOrder": {
                    "order": {
                        "orders": [
                            {
                                "a": 0,
                                "b": false,
                                "p": "170",
                                "s": "0.12",
                                "r": false,
                                "t": {
                                    "limit": {
                                        "tif": "Gtc"
                                    }
                                }
                            }
                        ],
                        "grouping": "na"
                    },
                    "orderResponse": [
                        {
                            "filled": {
                                "totalSz": "0.12",
                                "avgPx": "196.82",
                                "oid": 36105084161
                            }
                        }
                    ],
                    "symbol": "SOL",
                    "tokenImage": "https://app.hyperliquid.xyz/coins/SOL.svg",
                    "leverage": 10,
                    "leverageType": "Cross"
                }
            },
            "isHyperliquidMainnet": false
        }"#;

        let request: RecordActivityRequest = serde_json::from_str(perps_order_limit_json).unwrap();
        assert!(request.activities.perps_order.is_some());
        let perps_order = request.activities.perps_order.as_ref().unwrap();
        assert_eq!(perps_order.symbol, "SOL");
        assert_eq!(perps_order.leverage, 10.0);
        assert_eq!(perps_order.leverage_type, "Cross");
        assert!(perps_order.order.is_some());
        assert!(perps_order.order_response.is_some());

        // Test perpsOrder with trigger order sample
        let perps_order_trigger_json = r#"{
            "wallet_address": "******************************************",
            "activities": {
                "perpsOrder": {
                    "order": {
                        "orders": [
                            {
                                "a": 0,
                                "b": false,
                                "p": "160",
                                "s": "0.1",
                                "r": false,
                                "t": {
                                    "trigger": {
                                        "isMarket": true,
                                        "tpsl": "sl",
                                        "triggerPx": "200"
                                    }
                                }
                            }
                        ],
                        "grouping": "na"
                    },
                    "orderResponse": [
                        {
                            "resting": {
                                "oid": 36105356518
                            }
                        }
                    ],
                    "symbol": "SOL",
                    "tokenImage": "https://app.hyperliquid.xyz/coins/SOL.svg",
                    "leverage": 10,
                    "leverageType": "Cross"
                }
            },
            "isHyperliquidMainnet": false
        }"#;

        let request: RecordActivityRequest =
            serde_json::from_str(perps_order_trigger_json).unwrap();
        assert!(request.activities.perps_order.is_some());

        // Test perpsOrder with TWAP order sample
        let perps_order_twap_json = r#"{
            "wallet_address": "******************************************",
            "activities": {
                "perpsOrder": {
                    "twapOrder": {
                        "a": 0,
                        "b": false,
                        "s": "0.5",
                        "r": false,
                        "m": 5,
                        "t": false
                    },
                    "twapOrderResponse": {
                        "running": {
                            "twapId": 7260
                        }
                    },
                    "symbol": "SOL",
                    "tokenImage": "https://app.hyperliquid.xyz/coins/SOL.svg",
                    "leverage": 10,
                    "leverageType": "Cross"
                }
            },
            "isHyperliquidMainnet": false
        }"#;

        let request: RecordActivityRequest = serde_json::from_str(perps_order_twap_json).unwrap();
        assert!(request.activities.perps_order.is_some());
        let perps_order = request.activities.perps_order.as_ref().unwrap();
        assert!(perps_order.twap_order.is_some());
        assert!(perps_order.twap_order_response.is_some());

        // Test twapFillsHistory sample
        let twap_fills_json = r#"{
            "wallet_address": "******************************************",
            "activities": {
                "twapFillsHistory": {
                    "user": "******************************************",
                    "twapSliceFills": [
                        {
                            "fill": {
                                "coin": "SOL",
                                "px": "196.65",
                                "sz": "0.06",
                                "side": "A",
                                "time": 1753171810145,
                                "startPosition": "0.09",
                                "dir": "Close Long",
                                "closedPnl": "-0.1794",
                                "hash": "0x0000000000000000000000000000000000000000000000000000000000000000",
                                "oid": 36104706517,
                                "crossed": true,
                                "fee": "0.005309",
                                "tid": 234621612604218,
                                "feeToken": "USDC"
                            },
                            "twapId": 7260
                        }
                    ]
                }
            },
            "isHyperliquidMainnet": false
        }"#;

        let request: RecordActivityRequest = serde_json::from_str(twap_fills_json).unwrap();
        assert!(request.activities.twap_fills_history.is_some());
        let twap_fills = request.activities.twap_fills_history.as_ref().unwrap();
        assert_eq!(twap_fills.user, "******************************************");
        assert_eq!(twap_fills.twap_slice_fills.len(), 1);
        assert_eq!(twap_fills.twap_slice_fills[0].twap_id, 7260);

        // Test twapFillsHistory with isSnapshot
        let twap_fills_snapshot_json = r#"{
            "wallet_address": "******************************************",
            "activities": {
                "twapFillsHistory": {
                    "isSnapshot": true,
                    "user": "******************************************",
                    "twapSliceFills": [
                        {
                            "fill": {
                                "coin": "BTC",
                                "px": "108468.0",
                                "sz": "0.00017",
                                "side": "B",
                                "time": 1751967863063,
                                "startPosition": "0.0003",
                                "dir": "Open Long",
                                "closedPnl": "0.0",
                                "hash": "0x0000000000000000000000000000000000000000000000000000000000000000",
                                "oid": 35004172302,
                                "crossed": true,
                                "fee": "0.008297",
                                "tid": 1103725004643886,
                                "feeToken": "USDC"
                            },
                            "twapId": 7071
                        }
                    ]
                }
            },
            "isHyperliquidMainnet": false
        }"#;

        let request: RecordActivityRequest =
            serde_json::from_str(twap_fills_snapshot_json).unwrap();
        assert!(request.activities.twap_fills_history.is_some());
        let twap_fills = request.activities.twap_fills_history.as_ref().unwrap();
        assert_eq!(twap_fills.is_snapshot, Some(true));
    }
}
