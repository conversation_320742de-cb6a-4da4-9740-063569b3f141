use crate::{
    parsers::Parsers,
    processors::{perp_books::PerpBooksProcessor, state::StateProcessor},
};
use superstack_data::{postgres::PostgresDatabase, redis::RedisClient};
use tokio_util::sync::CancellationToken;

pub mod perp_books;
pub mod state;

pub struct Processors {
    pub perp_books: PerpBooksProcessor,
    pub state: StateProcessor,
    cancel: CancellationToken,
}

impl Processors {
    pub fn new(
        parsers: &mut Parsers,
        redis_client: RedisClient,
        pg_client: PostgresDatabase,
        cancel: CancellationToken,
    ) -> Self {
        // Subscribe to parsers data
        let l4_book_persist_rx = parsers.l4_snapshot.subscribe("l4_book_persister");
        let state_persist_rx = parsers.state.subscribe("state_persister");

        // Create processors with their receivers
        let perp_books = PerpBooksProcessor::new(redis_client.clone(), l4_book_persist_rx);
        let state = StateProcessor::new(redis_client, pg_client, state_persist_rx);

        Self { state, cancel, perp_books }
    }

    /// Run both processors concurrently with centralized cancellation management
    pub async fn run(self) {
        let perp_books_task = tokio::spawn(async move {
            self.perp_books.run().await;
        });

        let state_task = tokio::spawn(async move {
            self.state.run().await;
        });

        // Store abort handles
        let perp_books_abort = perp_books_task.abort_handle();
        let state_abort = state_task.abort_handle();

        // Use select! to handle cancellation - centralized cancellation management
        tokio::select! {
            _ = self.cancel.cancelled() => {
                tracing::info!("processors cancelled, aborting tasks");
            }
            perp_books_result = perp_books_task => {
                if let Err(e) = perp_books_result {
                    tracing::error!("perp_books processor task failed: {e}");
                }
            }
            state_result = state_task => {
                if let Err(e) = state_result {
                    tracing::error!("state processor task failed: {e}");
                }
            }
        }

        // Wait for tasks to complete
        perp_books_abort.abort();
        state_abort.abort();
        tracing::info!("processors stopped");
    }
}
