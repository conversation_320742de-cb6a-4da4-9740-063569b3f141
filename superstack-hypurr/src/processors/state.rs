use superstack_data::{postgres::PostgresDatabase, redis::RedisClient};
use tokio::sync::{mpsc::Receiver, oneshot};

use crate::parsers::state::StateMessage;

pub struct StateProcessor {
    redis_client: RedisClient,
    db_client: PostgresDatabase,
    rx: Receiver<(StateMessage, oneshot::Sender<()>)>,
}

impl StateProcessor {
    pub fn new(
        redis_client: RedisClient,
        db_client: PostgresDatabase,
        rx: Receiver<(StateMessage, oneshot::Sender<()>)>,
    ) -> Self {
        Self { redis_client, db_client, rx }
    }

    async fn process_state_msg(
        &self,
        StateMessage { perps, accts }: StateMessage,
    ) -> Result<(), anyhow::Error> {
        // save user states to db
        // let pg = self.db_client.clone();
        // let user_states_task = tokio::spawn(async move {
        //     if let Err(e) = pg.insert_or_update_perp_user_states_chunked(&accts,
        // Some(1000)).await {         tracing::error!("Failed to save user states: {}", e);
        //     }
        // });

        // perps
        let redis = self.redis_client.clone();
        let perps_task = tokio::spawn(async move {
            let mut perps_ok = 0;
            for perp in perps.into_values() {
                if let Err(e) = redis.set_perp_state(&perp).await {
                    tracing::error!("Failed to write perp state: {}", e);
                } else {
                    tracing::info!("Wrote perp state for token: {}", perp.perp_id);
                    perps_ok += 1;
                }
            }
            tracing::info!("Wrote {} perp states", perps_ok);
        });

        // wait for tasks to complete
        // tokio::try_join!(user_states_task, perps_task)?;
        tokio::try_join!(perps_task)?;

        Ok(())
    }

    pub async fn run(mut self) {
        tracing::info!("perp state processor started");
        loop {
            match self.rx.recv().await {
                Some((state_msg, confirm)) => {
                    tracing::info!("processing state with {} tokens", state_msg.perps.len());

                    if let Err(e) = self.process_state_msg(state_msg).await {
                        tracing::error!("Error processing state: {}", e);
                    }

                    if confirm.send(()).is_err() {
                        tracing::error!("Failed to send confirmation");
                    }
                }
                None => break,
            }
        }
        tracing::info!("perp state processor stopped");
    }
}
