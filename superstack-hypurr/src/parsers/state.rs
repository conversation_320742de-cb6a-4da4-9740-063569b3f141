use alloy::primitives::map::HashSet;
use anyhow::Result;
use bigdecimal::ToPrimitive;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, fs::File, path::PathBuf};
use superstack_data::postgres::{
    indexer::{perp_state::PerpBriefState, perp_user_state::PerpUserState},
    PerpExchange,
};
use tokio::sync::{broadcast, mpsc, oneshot};

use crate::types::state::State;

type AsyncTxConfirm = mpsc::Sender<(StateMessage, oneshot::Sender<()>)>;
type AsyncRxConfirm = mpsc::Receiver<(StateMessage, oneshot::Sender<()>)>;

const USDC_DECIMALS: u32 = 6;
const LEADERBOARD_USERS_PERCENT: u32 = 20;

pub struct StateParser {
    abci_state_rx: broadcast::Receiver<PathBuf>,
    state_ch_async: Vec<(&'static str, AsyncTxConfirm)>,
}

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
struct AccountStat {
    pub user_addr: String,
    pub account_value: u128,
    pub perps_account_value: u128,
    pub total_ntl: u128,
    pub c: i64,
    pub p: i64,
    pub positions: Vec<BriefPosition>,
}

impl From<AccountStat> for PerpUserState {
    fn from(account: AccountStat) -> Self {
        let account_value = account.account_value as f64 / 10_f64.powf(USDC_DECIMALS as f64);
        let perps_account_value =
            account.perps_account_value as f64 / 10_f64.powf(USDC_DECIMALS as f64);

        let hyperliquid_acc_collateral = account.c as f64 / 100_f64;
        let hyperliquid_acc_perps = account.p as f64 / 100_f64;

        Self {
            perp_exchange: PerpExchange::Hyperliquid,
            perp_user: account.user_addr,
            perps_account_value,
            account_value,
            hyperliquid_acc_collateral,
            hyperliquid_acc_perps,
            timestamp_millis: 0, // modify when persist
        }
    }
}

#[derive(Clone, Debug, Serialize, Deserialize)]
struct BriefPosition {
    pub perp_name: String,
    pub ntl: u128,
    pub is_long: bool,
    pub entry_amt: u128,
    pub entry_sz: u128,
    pub sz_decimals: u32,
}

impl BriefPosition {
    pub fn new(
        perp_name: String,
        ntl: u128,
        is_long: bool,
        entry_amt: u128,
        entry_sz: u128,
        sz_decimals: u32,
    ) -> Self {
        Self { perp_name, ntl, is_long, entry_amt, entry_sz, sz_decimals }
    }
}

#[derive(Debug, Default)]
struct AggregatedPerpState {
    long_ntl: u128,
    short_ntl: u128,
    long_traders: u64,
    short_traders: u64,
    long_entry_amt_acc: u128,
    long_entry_sz_acc: u128,
    short_entry_amt_acc: u128,
    short_entry_sz_acc: u128,
    sz_decimals: u32,
}

impl AggregatedPerpState {
    pub fn to_perp_brief_state(self, perp_id: String, updated_at_millis: i64) -> PerpBriefState {
        PerpBriefState {
            perp_id,
            long_ntl: self.long_ntl,
            short_ntl: self.short_ntl,
            long_traders: self.long_traders,
            short_traders: self.short_traders,
            long_entry: Self::avg_entry_px(
                self.long_entry_amt_acc,
                self.long_entry_sz_acc,
                self.sz_decimals,
            ),
            short_entry: Self::avg_entry_px(
                self.short_entry_amt_acc,
                self.short_entry_sz_acc,
                self.sz_decimals,
            ),
            updated_at_millis,
        }
    }

    // entry decimals = 6 (usdc decimals)
    // sz decimals depends on perp, e.g. 5 for BTC
    // entry_px = (entry / 10**6) / (sz / 10**sz_decimals) in $
    // so avg = (sum(entry) / 10**6) / (sum(sz) / 10**sz_decimals) in $
    // = (sum(entry) / sum(sz)) * 10**(sz_decimals - 6) in $
    fn avg_entry_px(amt_acc: u128, sz_acc: u128, sz_decimals: u32) -> f64 {
        let amt = bigdecimal::BigDecimal::from(amt_acc);
        let exp = sz_decimals as i64 - USDC_DECIMALS as i64;
        let (int_val, scale) = amt.into_bigint_and_scale();
        let amt = bigdecimal::BigDecimal::from_bigint(int_val, scale - exp);
        let res = amt / sz_acc;
        res.to_f64().unwrap_or_default()
    }
}

#[derive(Clone)]
pub struct StateMessage {
    // todo(lythesia): currently no need coz leaderboard is not planned
    pub accts: Vec<PerpUserState>,
    pub perps: HashMap<String, PerpBriefState>,
}

impl StateParser {
    pub fn new(abci_state_rx: broadcast::Receiver<PathBuf>) -> Self {
        Self { abci_state_rx, state_ch_async: vec![] }
    }

    pub fn subscribe(&mut self, name: &'static str) -> AsyncRxConfirm {
        tracing::info!("registering async consumer {}", name);
        let (tx, rx) = tokio::sync::mpsc::channel(100);
        self.state_ch_async.push((name, tx));
        rx
    }

    // 1. perp_name -> [(user, postion of perp)]
    // 2. top users by perp
    #[allow(unused)]
    fn aggregate_accounts_by_perp(
        state: &State,
    ) -> HashMap<String, HashMap<String, Vec<BriefPosition>>> {
        // 1st pass: collect positions & ntl
        let mut user_positions_by_perp: HashMap<String, HashMap<String, Vec<BriefPosition>>> =
            HashMap::new();
        let mut users_ntl_by_perp: HashMap<String, HashMap<String, u128>> = HashMap::new();
        for (user_addr, user_stat) in state.user_states.iter() {
            for position in user_stat.positions.iter() {
                let asset_idx = position.asset_idx;
                let perp_name = match state.meta.get_perp_name_by_idx(asset_idx) {
                    Some(name) => name,
                    None => {
                        tracing::error!("perp not found for asset_idx {asset_idx}");
                        continue;
                    }
                };
                let perp_px = match state.meta.get_perp_px_by_idx(asset_idx) {
                    Some(px) => px,
                    None => {
                        tracing::error!("perp px not found for asset_idx {asset_idx}");
                        continue;
                    }
                };
                let sz_decimals = match state.meta.get_perp_sz_decimals_by_idx(asset_idx) {
                    Some(decimals) => decimals,
                    None => {
                        tracing::error!("perp sz decimals not found for asset_idx {asset_idx}");
                        continue;
                    }
                };
                let ntl = position.notional(perp_px);
                let ntl_abs = ntl.abs() as u128;

                // collect user positions by perp
                let entry = user_positions_by_perp
                    .entry(perp_name.to_owned())
                    .or_default()
                    .entry(user_addr.to_string())
                    .or_default();
                entry.push(BriefPosition::new(
                    perp_name.to_owned(),
                    ntl_abs,
                    ntl > 0,
                    position.entry_amount as _,
                    position.size as _,
                    sz_decimals as _,
                ));

                // collect users ntl by perp
                let entry = users_ntl_by_perp
                    .entry(perp_name.to_owned())
                    .or_default()
                    .entry(user_addr.to_string())
                    .or_default();
                *entry += ntl_abs;
            }
        }

        // 2nd pass: filter top users
        let top_users_by_perp = users_ntl_by_perp
            .into_iter()
            .map(|(perp_name, users)| {
                let mut users: Vec<(String, u128)> =
                    users.into_iter().map(|(user_addr, ntl)| (user_addr, ntl)).collect();
                let index =
                    (users.len() as f64 * LEADERBOARD_USERS_PERCENT as f64 / 100.0) as usize;
                users.select_nth_unstable_by_key(index, |v| std::cmp::Reverse(v.1));
                users.truncate(index);
                (
                    perp_name,
                    users.into_iter().map(|(user_addr, ntl)| user_addr).collect::<HashSet<_>>(),
                )
            })
            .collect::<HashMap<_, _>>();

        // 3. filter out non-top users from user_positions_by_perp
        for (perp_name, user_positions) in user_positions_by_perp.iter_mut() {
            if let Some(top_users) = top_users_by_perp.get(perp_name) {
                user_positions.retain(|user_addr, _| top_users.contains(user_addr));
            }
        }

        user_positions_by_perp
    }

    fn aggregate_accounts(state: &State) -> Vec<AccountStat> {
        let mut accounts = vec![];

        for (user_addr, user_stat) in state.user_states.iter() {
            let mut total_ntl = 0u128;
            let mut positions = vec![];

            for position in user_stat.positions.iter() {
                let perp_name = match state.meta.get_perp_name_by_idx(position.asset_idx) {
                    Some(name) => name,
                    None => {
                        tracing::error!("perp not found for asset_idx {}", position.asset_idx);
                        continue;
                    }
                };
                let perp_px = match state.meta.get_perp_px_by_idx(position.asset_idx) {
                    Some(px) => px,
                    None => {
                        tracing::error!("perp px not found for asset_idx {}", position.asset_idx);
                        continue;
                    }
                };
                let sz_decimals = match state.meta.get_perp_sz_decimals_by_idx(position.asset_idx) {
                    Some(decimals) => decimals,
                    None => {
                        tracing::error!(
                            "perp sz decimals not found for asset_idx {}",
                            position.asset_idx
                        );
                        continue;
                    }
                };

                let ntl = position.notional(perp_px);
                let ntl_abs = ntl.abs() as u128;
                total_ntl += ntl_abs;

                positions.push(BriefPosition::new(
                    perp_name.to_string(),
                    ntl_abs,
                    ntl > 0,
                    position.entry_amount as _,
                    position.size.unsigned_abs() as _,
                    sz_decimals as _,
                ));
            }

            let account_value =
                user_stat.account_value(&state.meta, &state.spot_meta, &state.spot_books);
            let perps_account_value = user_stat.perps_account_value(&state.meta);

            accounts.push(AccountStat {
                user_addr: user_addr.to_string(),
                account_value,
                perps_account_value,
                total_ntl,
                c: user_stat.c,
                p: user_stat.p,
                positions,
            });
        }

        // filter top users
        let index = (accounts.len() as f64 * LEADERBOARD_USERS_PERCENT as f64 / 100.0) as usize;
        accounts.select_nth_unstable_by_key(index, |v| std::cmp::Reverse(v.total_ntl));
        accounts.truncate(index);

        accounts
    }

    fn aggregate_perps(accounts: &Vec<AccountStat>) -> HashMap<String, PerpBriefState> {
        let mut perps: HashMap<String, AggregatedPerpState> = HashMap::new();

        for account in accounts {
            for position in &account.positions {
                let perp_id = position.perp_name.clone();
                let entry = perps.entry(perp_id.clone()).or_insert_with(|| AggregatedPerpState {
                    sz_decimals: position.sz_decimals,
                    ..Default::default()
                });
                if position.is_long {
                    entry.long_ntl += position.ntl;
                    entry.long_traders += 1;
                    entry.long_entry_amt_acc += position.entry_amt;
                    entry.long_entry_sz_acc += position.entry_sz;
                } else {
                    entry.short_ntl += position.ntl;
                    entry.short_traders += 1;
                    entry.short_entry_amt_acc += position.entry_amt;
                    entry.short_entry_sz_acc += position.entry_sz;
                }
            }
        }

        let now = chrono::Utc::now().timestamp_millis();
        perps
            .into_iter()
            .map(|(perp_id, state)| (perp_id.clone(), state.to_perp_brief_state(perp_id, now)))
            .collect()
    }

    async fn process(&mut self, rmp: &PathBuf, last_height: &mut u64) -> Result<()> {
        let rmp = rmp.clone();
        let lh = *last_height;
        let msg = tokio::task::spawn_blocking(move || {
            // 1. decode rmp
            tracing::info!("decoding {}", rmp.display());
            let now = std::time::Instant::now();
            let file = File::open(&rmp)?;
            let md = file.metadata()?;
            tracing::info!(
                "file last modify: {}",
                chrono::DateTime::<chrono::Utc>::from(md.modified()?)
            );
            let mut cursor = std::io::BufReader::new(file);
            let value: rmpv::Value = rmpv::decode::read_value(&mut cursor)?;
            tracing::info!("decoded in {:?}", now.elapsed());

            // 2. parse state
            tracing::info!("parsing {}", rmp.display());
            let now = std::time::Instant::now();
            let state = State::from_rmpv_value(&value)?;
            tracing::info!("parsed in {:?}", now.elapsed());

            let latest = state.context.height;

            if latest == lh {
                tracing::info!("height {} is the same as last height, skipping", latest);
                return Ok(None);
            }

            // 3. aggregate
            let accts = Self::aggregate_accounts(&state);
            let perps = Self::aggregate_perps(&accts);
            tracing::info!("#account = {}, #pers = {}", accts.len(), perps.len());
            let accts = accts.into_iter().map(|a| a.into()).collect();
            let state = StateMessage { accts, perps };
            Ok::<_, anyhow::Error>(Some((state, latest)))
        })
        .await??;

        if let Some((msg, latest)) = msg {
            // 4. persist async
            let mut remove = vec![];
            for (name, tx) in self.state_ch_async.iter() {
                tracing::info!("notifying async consumer {}", name);
                let (ctx, crx) = oneshot::channel();
                if tx.send((msg.clone(), ctx)).await.is_err() {
                    tracing::error!("async consumer {} disconnected, removing", name);
                    remove.push(name as &'static str);
                }
                if crx.await.is_err() {
                    tracing::error!("async consumer {} (confirm) disconnected, removing", name);
                    remove.push(name as &'static str);
                }
            }
            self.state_ch_async.retain(|(name, _)| !remove.contains(name));

            *last_height = latest;
        }

        Ok(())
    }

    pub async fn run(mut self) {
        tracing::info!("state_parser start");
        let mut last_height = 0;
        loop {
            match self.abci_state_rx.recv().await {
                Ok(rmp) => {
                    tracing::info!("processing rmp {}", rmp.display());

                    if let Err(e) = self.process(&rmp, &mut last_height).await {
                        tracing::error!("error processing rmp: {e}");
                    } else {
                        tracing::info!("processed rmp");
                    }
                }
                Err(_) => {
                    tracing::error!("channel disconnected");
                    break;
                }
            }
        }
        tracing::info!("state_parser stopped");
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_aggregate_accounts() {
        superstack_data::utils::setup_tracing();

        let rmp = PathBuf::from("src/types/samples/abci_state.rmp");
        let file = File::open(&rmp).unwrap();
        let mut cursor = std::io::BufReader::new(file);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();
        let state = State::from_rmpv_value(&value).unwrap();
        let accounts = StateParser::aggregate_accounts(&state);

        let write_file = std::fs::File::create("src/types/samples/account_values.json").unwrap();
        serde_json::to_writer_pretty(write_file, &accounts).unwrap();
    }
}
