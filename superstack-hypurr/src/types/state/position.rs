use serde::{Deserialize, Serialize};

use crate::utils::find_value;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub struct Position {
    /// asset index
    pub asset_idx: u64,
    /// margin table id
    pub margin_table_id: Option<u64>,
    /// entry usdc amount, decimal 6
    pub entry_amount: u64,
    /// leverage
    pub leverage: Option<Leverage>,
    /// size, positive means long, negative means short
    pub size: i64,
    /// funding
    pub funding: Option<Funding>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct Funding {
    /// all funding
    all_time: Option<i64>,
    /// since change
    since_change: Option<i64>,
    /// open funding
    since_open: Option<i64>,
}

impl Funding {
    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let all_time = find_value(value, "a").and_then(|v| v.as_i64());
        let since_change = find_value(value, "c").and_then(|v| v.as_i64());
        let since_open = find_value(value, "o").and_then(|v| v.as_i64());

        Ok(Self { all_time, since_change, since_open })
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum Leverage {
    /// (leverage, raw_usd)
    Isolated((u64, i64)),
    /// leverage
    Cross(u64),
}

impl Leverage {
    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let c = find_value(value, "C").and_then(|v| v.as_u64());
        match c {
            Some(c) => Ok(Leverage::Cross(c)),
            None => {
                let i = find_value(value, "I").ok_or(anyhow::anyhow!("invalid mode"))?;
                let l = find_value(i, "l")
                    .and_then(|v| v.as_u64())
                    .ok_or(anyhow::anyhow!("invalid isolated leverage"))?;
                let u = find_value(i, "u")
                    .and_then(|v| v.as_i64())
                    .ok_or(anyhow::anyhow!("invalid isolated used_usd"))?;

                Ok(Leverage::Isolated((l, u)))
            }
        }
    }
}

impl Position {
    pub fn notional(&self, oracle_px: u64) -> i128 {
        self.size as i128 * oracle_px as i128
    }

    pub fn notional_abs(&self, oracle_px: u64) -> u128 {
        self.size.unsigned_abs() as u128 * oracle_px as u128
    }

    /// if sz == 0, it's None
    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Option<Self>> {
        let item = value.as_array().ok_or(anyhow::anyhow!("invalid item"))?;
        let asset_idx = item[0].as_u64().ok_or(anyhow::anyhow!("invalid asset_idx"))?;

        let size = find_value(&item[1], "s").and_then(|v| v.as_i64());
        if let Some(size) = size {
            let entry_amount = find_value(&item[1], "e")
                .and_then(|v| v.as_u64())
                .ok_or(anyhow::anyhow!("invalid entry_amount"))?;
            let leverage =
                find_value(&item[1], "l").and_then(|v| Leverage::from_rmpv_value(v).ok());
            let funding = find_value(&item[1], "f").and_then(|v| Funding::from_rmpv_value(v).ok());

            return Ok(Some(Self {
                asset_idx,
                margin_table_id: None,
                entry_amount,
                leverage,
                size,
                funding,
            }));
        }

        Ok(None)
    }
}
