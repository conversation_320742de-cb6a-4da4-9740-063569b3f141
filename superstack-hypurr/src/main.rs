use std::path::PathBuf;

use superstack_data::{postgres::PostgresDatabase, redis::RedisClient, utils::setup_tracing};
use superstack_hypurr::{
    cmd::HlNodeCmd,
    config::Config,
    parsers::Parsers,
    processors::Processors,
    watcher::{FileWatcher, Upstream},
};
use tokio::signal;
use tokio_util::sync::CancellationToken;

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();
    setup_tracing();

    let config = Config::get();
    tracing::info!("config: {:?}", config);

    let hl_node =
        HlNodeCmd::new(PathBuf::from(&config.hl_node)).await.expect("`hl-node` executable");

    let redis_client = RedisClient::new(&config.redis_url).await.expect("redis");
    let pg = PostgresDatabase::new(&config.pg_url, config.pg_max_connections, true)
        .await
        .expect("postgres");

    // watcher
    let rmp_watcher = FileWatcher::new(
        PathBuf::from(&config.hl_dir).join("hyperliquid_data").join("abci_state.rmp"),
        config.watch_interval_secs,
        10, // channel_capacity
    );

    let cancel = CancellationToken::new();

    // Create components
    let upstream = Upstream::new(rmp_watcher, cancel.clone());
    let mut parsers = Parsers::new(&upstream, hl_node, cancel.clone());
    let processors = Processors::new(&mut parsers, redis_client, pg, cancel.clone());

    // Start All components
    let upstream_handle = tokio::spawn(upstream.run());
    let parser_handle = tokio::spawn(parsers.run());
    let processors_handle = tokio::spawn(processors.run());

    // ctrl-c
    tokio::select! {
        _ = signal::ctrl_c() => {
            tracing::info!("ctrlc received, cancelling");
            cancel.cancel();
        }
    }

    tokio::try_join!(upstream_handle, parser_handle, processors_handle).expect("tokio::try_join");
}
